"""
FinSight AI - Main Streamlit Application
PDF-based Annual Report Analysis Chatbot for Castrol, Veedol, and Valvoline
"""

import streamlit as st
import logging
from pathlib import Path
import sys
from typing import Dict, List
import time
from dotenv import load_dotenv

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# Load environment variables
env_path = current_dir / ".env"
load_dotenv(env_path)

from config.config import get_system_config, STREAMLIT_CONFIG
from utils.chatbot_engine import FinSightChatbot
from utils.document_processor import DocumentProcessor
from utils.llm_setup import test_llm_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(**STREAMLIT_CONFIG)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f4e79;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .company-card {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
        background-color: #f8f9fa;
    }
    
    .company-header {
        font-weight: bold;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-ready {
        background-color: #28a745;
    }
    
    .status-warning {
        background-color: #ffc107;
    }
    
    .status-error {
        background-color: #dc3545;
    }
    
    .chat-message {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 10px;
    }
    
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    
    .assistant-message {
        background-color: #f1f8e9;
        border-left: 4px solid #4caf50;
    }
    
    .source-info {
        font-size: 0.8rem;
        color: #666;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background-color: #f5f5f5;
        border-radius: 5px;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def initialize_system():
    """Initialize the FinSight AI system"""
    try:
        config = get_system_config()
        chatbot = FinSightChatbot()
        doc_processor = DocumentProcessor(config)
        
        # Test LLM connection
        llm_status = test_llm_connection()
        
        return chatbot, doc_processor, config, llm_status
    except Exception as e:
        st.error(f"Failed to initialize system: {str(e)}")
        return None, None, None, False

def display_company_status(doc_processor: DocumentProcessor):
    """Display status of each company's documents"""
    st.subheader("📊 Company Document Status")
    
    stats = doc_processor.get_document_stats()
    
    cols = st.columns(3)
    
    for i, (company_key, company_stats) in enumerate(stats.items()):
        with cols[i % 3]:
            # Determine status
            if company_stats['pdf_count'] > 0 and company_stats['has_vectors']:
                status_class = "status-ready"
                status_text = "Ready"
            elif company_stats['pdf_count'] > 0:
                status_class = "status-warning"
                status_text = "Needs Processing"
            else:
                status_class = "status-error"
                status_text = "No Documents"
            
            st.markdown(f"""
            <div class="company-card">
                <div class="company-header" style="color: {company_stats['color']}">
                    <span class="status-indicator {status_class}"></span>
                    {company_stats['display_name']}
                </div>
                <div>Status: {status_text}</div>
                <div>Documents: {company_stats['pdf_count']}</div>
                <div>Vector Index: {'✅' if company_stats['has_vectors'] else '❌'}</div>
            </div>
            """, unsafe_allow_html=True)
            
            if company_stats['pdf_files']:
                with st.expander(f"View {company_stats['display_name']} Files"):
                    for file in company_stats['pdf_files']:
                        st.write(f"📄 {file}")

def display_chat_interface(chatbot: FinSightChatbot):
    """Display the main chat interface"""
    st.subheader("💬 Chat with FinSight AI")

    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []
        st.session_state.messages.append({
            "role": "assistant",
            "content": "Hello! I'm FinSight AI, your financial analysis assistant. I can help you analyze annual reports from Castrol, Veedol, and Valvoline. What would you like to know?"
        })

    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

            # Display sources if available
            if message["role"] == "assistant" and "sources" in message:
                if message["sources"]:
                    with st.expander("📚 Sources"):
                        for source in message["sources"]:
                            st.write(f"• **{source['company']}** - {source['file']} (Page: {source['page']})")

def handle_chat_input(chatbot: FinSightChatbot):
    """Handle chat input processing"""
    # Chat input
    if prompt := st.chat_input("Ask about financial performance, strategy, risks, or compare companies..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})

        # Generate and display assistant response
        with st.spinner("Analyzing documents..."):
            response_data = chatbot.generate_response(prompt)

            # Add assistant message to chat history with metadata
            assistant_message = {
                "role": "assistant",
                "content": response_data['response'],
                "sources": response_data.get('sources', []),
                "companies_analyzed": response_data.get('companies_analyzed', []),
                "query_type": response_data.get('query_type', 'general')
            }
            st.session_state.messages.append(assistant_message)

        # Rerun to display the new messages
        st.rerun()

def display_sidebar(doc_processor: DocumentProcessor, chatbot: FinSightChatbot):
    """Display sidebar with controls and information"""
    with st.sidebar:
        st.title("🎯 FinSight AI")
        st.markdown("*Annual Report Analysis*")
        
        st.divider()
        
        # System status
        st.subheader("🔧 System Status")
        
        # LLM status
        if st.button("Test LLM Connection"):
            with st.spinner("Testing..."):
                llm_status = test_llm_connection()
                if llm_status:
                    st.success("✅ LLM Connection OK")
                else:
                    st.error("❌ LLM Connection Failed")
        
        st.divider()
        
        # Document management
        st.subheader("📁 Document Management")
        
        # Refresh vector stores
        if st.button("🔄 Refresh All Indexes"):
            with st.spinner("Refreshing document indexes..."):
                results = chatbot.refresh_document_index()
                
                for company, success in results.items():
                    if success:
                        st.success(f"✅ {company.title()} index refreshed")
                    else:
                        st.warning(f"⚠️ {company.title()} index refresh failed")
        
        # Clear chat history
        if st.button("🗑️ Clear Chat History"):
            st.session_state.messages = []
            chatbot.clear_conversation_history()
            st.success("Chat history cleared!")
            st.rerun()
        
        st.divider()
        
        # Usage tips
        st.subheader("💡 Usage Tips")
        st.markdown("""
        **Sample Questions:**
        - "Compare the revenue growth of all three companies"
        - "What are Castrol's main risk factors?"
        - "Analyze Valvoline's sustainability initiatives"
        - "Show me Veedol's strategic priorities"
        - "Compare profitability across companies"
        """)
        
        st.divider()
        
        # About
        st.subheader("ℹ️ About")
        st.markdown("""
        FinSight AI analyzes annual reports from:
        - **Castrol** 🟢
        - **Veedol** 🟠  
        - **Valvoline** 🔴
        
        Upload PDF annual reports to the respective company folders to get started.
        """)

def main():
    """Main application function"""
    # Initialize system
    chatbot, doc_processor, config, llm_status = initialize_system()

    if not chatbot:
        st.error("Failed to initialize FinSight AI. Please check your configuration.")
        return

    # Main header
    st.markdown('<h1 class="main-header">🎯 FinSight AI</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; color: #666; font-size: 1.2rem;">Annual Report Analysis for Lubricant Industry Leaders</p>', unsafe_allow_html=True)

    # Display LLM status
    if not llm_status:
        st.warning("⚠️ LLM connection issue detected. Some features may not work properly.")

    # Sidebar
    display_sidebar(doc_processor, chatbot)

    # Check which tab is selected
    if "active_tab" not in st.session_state:
        st.session_state.active_tab = "chat"

    # Tab selection buttons
    col1, col2 = st.columns(2)
    with col1:
        if st.button("💬 Chat Analysis", use_container_width=True,
                    type="primary" if st.session_state.active_tab == "chat" else "secondary"):
            st.session_state.active_tab = "chat"
    with col2:
        if st.button("📊 Document Status", use_container_width=True,
                    type="primary" if st.session_state.active_tab == "status" else "secondary"):
            st.session_state.active_tab = "status"

    st.divider()

    # Display content based on active tab
    if st.session_state.active_tab == "chat":
        display_chat_interface(chatbot)
        # Chat input outside of any containers
        handle_chat_input(chatbot)
    else:
        display_company_status(doc_processor)

if __name__ == "__main__":
    main()
