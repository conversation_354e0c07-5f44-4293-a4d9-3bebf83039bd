# 🎯 FinSight AI - Annual Report Analysis System

FinSight AI is a specialized PDF-based chatbot system designed to analyze annual reports from three major lubricant industry companies: **Castrol**, **Veedol**, and **Valvoline**. The system provides intelligent financial analysis, comparative insights, and strategic intelligence through natural language queries.

## 🌟 Features

### Core Capabilities
- **PDF Document Processing**: Automated extraction and chunking of annual report content
- **Intelligent Retrieval**: Vector-based similarity search for relevant document sections
- **Multi-Company Analysis**: Simultaneous analysis across Castrol, Veedol, and Valvoline
- **Query Classification**: Automatic categorization of queries (financial, strategic, risk, sustainability, comparative)
- **Source Attribution**: Clear citation of information sources with page references

### Analysis Types
- **Financial Performance**: Revenue, profitability, growth metrics, financial ratios
- **Business Strategy**: Strategic initiatives, market expansion, investment priorities
- **Risk Analysis**: Risk factors, mitigation strategies, regulatory concerns
- **Sustainability & ESG**: Environmental initiatives, social responsibility, governance
- **Comparative Analysis**: Cross-company performance and strategic comparisons

### User Interface
- **Streamlit Web App**: Clean, intuitive chat interface
- **Real-time Processing**: Instant document retrieval and analysis
- **Document Status Dashboard**: Monitor document availability and processing status
- **Source Transparency**: View exact document sources for each response

## 🏗️ System Architecture

```
FinSight_AI/
├── app.py                      # Main Streamlit application
├── setup_documents.py          # Document processing utility
├── requirements.txt            # Python dependencies
├── .env.example               # Environment configuration template
├── config/
│   └── config.py              # System configuration
├── utils/
│   ├── document_processor.py  # PDF processing and vector storage
│   ├── llm_setup.py          # LLM initialization and management
│   └── chatbot_engine.py     # Main chatbot logic
├── documents/                 # PDF storage directories
│   ├── castrol/              # Castrol annual reports
│   ├── veedol/               # Veedol annual reports
│   └── valvoline/            # Valvoline annual reports
├── data/
│   ├── vectors/              # FAISS vector indexes
│   └── processed/            # Processed document metadata
└── logs/                     # Application logs
```

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone or navigate to the FinSight_AI directory
cd FinSight_AI

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
# Required: Either Azure OpenAI OR OpenAI API key
```

**Environment Variables:**
```env
# Azure OpenAI (Recommended)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name

# OR OpenAI (Alternative)
OPENAI_API_KEY=your_openai_api_key
```

### 3. Document Setup

```bash
# Add PDF annual reports to company directories:
# documents/castrol/     - Castrol annual reports
# documents/veedol/      - Veedol annual reports  
# documents/valvoline/   - Valvoline annual reports

# Process documents and create vector indexes
python setup_documents.py
```

### 4. Launch Application

```bash
# Start the Streamlit app
streamlit run app.py
```

The application will be available at `http://localhost:8501`

## 📊 Usage Examples

### Financial Analysis Queries
- "Compare the revenue growth of all three companies over the last year"
- "What is Castrol's profit margin and how does it compare to competitors?"
- "Show me the key financial metrics for Valvoline"

### Strategic Analysis Queries
- "What are Veedol's main strategic priorities for the next year?"
- "Compare the market expansion strategies of all three companies"
- "Analyze Castrol's investment in new technologies"

### Risk Analysis Queries
- "What are the main risk factors identified by Valvoline?"
- "Compare the risk management approaches across companies"
- "What regulatory risks do these companies face?"

### Sustainability Queries
- "Analyze the ESG initiatives of all three companies"
- "What are Castrol's environmental sustainability targets?"
- "Compare the sustainability commitments across companies"

## 🔧 System Components

### Document Processor (`utils/document_processor.py`)
- PDF loading and text extraction using PyPDFLoader
- Intelligent text chunking with overlap for context preservation
- FAISS vector store creation and management
- Document metadata tracking and source attribution

### LLM Manager (`utils/llm_setup.py`)
- Azure OpenAI and OpenAI integration with fallback support
- Connection testing and error handling
- Caching configuration for improved performance
- Environment-based configuration management

### Chatbot Engine (`utils/chatbot_engine.py`)
- Query classification and intent recognition
- Multi-company document retrieval
- Context-aware response generation
- Conversation history management
- Source attribution and citation

### Configuration (`config/config.py`)
- Company-specific settings and branding
- Model parameters and system configuration
- Analysis prompt templates
- Directory structure management

## 🎨 Customization

### Adding New Companies
1. Update `config/config.py` with new company configuration
2. Create document directory: `documents/new_company/`
3. Add PDF documents and run `setup_documents.py`

### Modifying Analysis Types
1. Edit `ANALYSIS_PROMPTS` in `config/config.py`
2. Update query classification logic in `chatbot_engine.py`
3. Customize UI elements in `app.py`

### Styling and Branding
- Modify CSS in `app.py` for custom styling
- Update company colors in `config/config.py`
- Customize page configuration in `STREAMLIT_CONFIG`

## 🔍 Troubleshooting

### Common Issues

**LLM Connection Failed**
- Verify API keys in `.env` file
- Check network connectivity
- Ensure correct endpoint URLs for Azure OpenAI

**No Documents Found**
- Verify PDF files are in correct company directories
- Check file permissions and formats
- Run `setup_documents.py` to reprocess

**Vector Store Errors**
- Delete existing vector stores in `data/vectors/`
- Rerun `setup_documents.py`
- Check disk space and permissions

**Performance Issues**
- Reduce `chunk_size` in model configuration
- Limit `top_k_retrieval` for faster responses
- Enable LLM caching (enabled by default)

## 📈 Performance Optimization

### Document Processing
- Optimal chunk size: 1000 characters with 200 overlap
- Use FastEmbed for efficient embeddings
- FAISS for fast similarity search

### Response Generation
- Query classification reduces processing time
- Intelligent company filtering based on query content
- LLM response caching for repeated queries

### Resource Management
- Vector stores cached in memory after first load
- Conversation history limited to recent exchanges
- Automatic cleanup of temporary files

## 🔒 Security Considerations

- API keys stored in environment variables
- No sensitive data logged
- Document content processed locally
- Vector stores stored locally (not cloud)

## 📝 License

This project is part of the SalesPulse system and follows the same licensing terms.

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review application logs in `logs/finsight_ai.log`
3. Test individual components using the setup utility

---

**FinSight AI** - Empowering financial analysis through intelligent document processing.
