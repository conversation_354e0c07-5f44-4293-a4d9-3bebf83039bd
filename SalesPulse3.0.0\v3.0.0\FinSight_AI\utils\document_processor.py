"""
Document Processing Module for FinSight AI
Handles PDF loading, text extraction, chunking, and vector storage
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import pickle
from datetime import datetime

from langchain_community.document_loaders import PyPDFLoader
from langchain_community.embeddings.fastembed import FastEmbedEmbeddings
from langchain_openai import AzureOpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import os

from config.config import get_system_config, SystemConfig

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Handles document processing for FinSight AI system"""
    
    def __init__(self, config: Optional[SystemConfig] = None):
        self.config = config or get_system_config()
        self.embeddings = self._get_embeddings()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.model_config.chunk_size,
            chunk_overlap=self.config.model_config.chunk_overlap,
            length_function=len,
        )

    def _get_embeddings(self):
        """Get the best available embeddings model"""
        # Try Azure OpenAI embeddings first
        try:
            azure_api_key = os.getenv("AZURE_OPENAI_API_KEY")
            azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
            azure_deployment = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT")

            if azure_api_key and azure_endpoint and azure_deployment:
                logger.info("Using Azure OpenAI embeddings")
                return AzureOpenAIEmbeddings(
                    openai_api_key=azure_api_key,
                    azure_endpoint=azure_endpoint,
                    deployment=azure_deployment,
                    openai_api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-05-15")
                )
        except Exception as e:
            logger.warning(f"Failed to initialize Azure OpenAI embeddings: {str(e)}")

        # Fallback to FastEmbed
        logger.info("Using FastEmbed embeddings as fallback")
        return FastEmbedEmbeddings(
            model_name=self.config.model_config.embedding_model
        )
        
    def load_pdf_documents(self, company: str) -> List[Document]:
        """Load all PDF documents for a specific company"""
        company_dir = self.config.documents_dir / company
        
        if not company_dir.exists():
            logger.warning(f"Company directory does not exist: {company_dir}")
            return []
        
        pdf_files = list(company_dir.glob("*.pdf"))
        if not pdf_files:
            logger.warning(f"No PDF files found in {company_dir}")
            return []
        
        all_documents = []
        
        for pdf_file in pdf_files:
            try:
                logger.info(f"Loading PDF: {pdf_file.name}")
                loader = PyPDFLoader(str(pdf_file))
                documents = loader.load()
                
                # Add metadata to each document
                for doc in documents:
                    doc.metadata.update({
                        'source_file': pdf_file.name,
                        'company': company,
                        'company_display': self.config.companies[company].display_name,
                        'document_type': 'annual_report',
                        'processed_date': datetime.now().isoformat(),
                        'file_path': str(pdf_file)
                    })
                
                # Split documents into chunks
                chunks = self.text_splitter.split_documents(documents)
                all_documents.extend(chunks)
                
                logger.info(f"Loaded {len(chunks)} chunks from {pdf_file.name}")
                
            except Exception as e:
                logger.error(f"Error loading {pdf_file.name}: {str(e)}")
                continue
        
        logger.info(f"Total chunks loaded for {company}: {len(all_documents)}")
        return all_documents
    
    def load_all_documents(self) -> Dict[str, List[Document]]:
        """Load documents for all companies"""
        all_company_docs = {}
        
        for company_key in self.config.companies.keys():
            docs = self.load_pdf_documents(company_key)
            if docs:
                all_company_docs[company_key] = docs
            
        return all_company_docs
    
    def create_vector_store(self, documents: List[Document], company: str) -> FAISS:
        """Create FAISS vector store for a company's documents"""
        if not documents:
            raise ValueError(f"No documents provided for {company}")
        
        logger.info(f"Creating vector store for {company} with {len(documents)} documents")
        
        # Create vector store
        vector_store = FAISS.from_documents(documents, self.embeddings)
        
        # Save vector store
        vector_path = self.config.vectors_dir / f"{company}_vectors"
        vector_store.save_local(str(vector_path))
        
        logger.info(f"Vector store saved to: {vector_path}")
        return vector_store
    
    def create_all_vector_stores(self) -> Dict[str, FAISS]:
        """Create vector stores for all companies"""
        company_docs = self.load_all_documents()
        vector_stores = {}
        
        for company, documents in company_docs.items():
            try:
                vector_store = self.create_vector_store(documents, company)
                vector_stores[company] = vector_store
                logger.info(f"Successfully created vector store for {company}")
            except Exception as e:
                logger.error(f"Failed to create vector store for {company}: {str(e)}")
        
        return vector_stores
    
    def load_vector_store(self, company: str) -> Optional[FAISS]:
        """Load existing vector store for a company"""
        vector_path = self.config.vectors_dir / f"{company}_vectors"
        
        if not vector_path.exists():
            logger.warning(f"Vector store not found for {company} at {vector_path}")
            return None
        
        try:
            vector_store = FAISS.load_local(
                str(vector_path), 
                self.embeddings,
                allow_dangerous_deserialization=True
            )
            logger.info(f"Loaded vector store for {company}")
            return vector_store
        except Exception as e:
            logger.error(f"Error loading vector store for {company}: {str(e)}")
            return None
    
    def load_all_vector_stores(self) -> Dict[str, FAISS]:
        """Load all existing vector stores"""
        vector_stores = {}
        
        for company_key in self.config.companies.keys():
            vector_store = self.load_vector_store(company_key)
            if vector_store:
                vector_stores[company_key] = vector_store
        
        return vector_stores
    
    def get_document_stats(self) -> Dict[str, Dict]:
        """Get statistics about loaded documents"""
        stats = {}
        
        for company_key, company_config in self.config.companies.items():
            company_dir = self.config.documents_dir / company_key
            pdf_files = list(company_dir.glob("*.pdf")) if company_dir.exists() else []
            
            vector_path = self.config.vectors_dir / f"{company_key}_vectors"
            has_vectors = vector_path.exists()
            
            stats[company_key] = {
                'display_name': company_config.display_name,
                'pdf_count': len(pdf_files),
                'pdf_files': [f.name for f in pdf_files],
                'has_vectors': has_vectors,
                'color': company_config.color
            }
        
        return stats
    
    def refresh_vector_store(self, company: str) -> bool:
        """Refresh vector store for a specific company"""
        try:
            documents = self.load_pdf_documents(company)
            if not documents:
                logger.warning(f"No documents found for {company}")
                return False
            
            self.create_vector_store(documents, company)
            logger.info(f"Successfully refreshed vector store for {company}")
            return True
            
        except Exception as e:
            logger.error(f"Error refreshing vector store for {company}: {str(e)}")
            return False
    
    def search_documents(self, query: str, company: str, k: int = 5) -> List[Document]:
        """Search documents for a specific company"""
        vector_store = self.load_vector_store(company)
        if not vector_store:
            logger.warning(f"No vector store found for {company}")
            return []
        
        try:
            results = vector_store.similarity_search(query, k=k)
            logger.info(f"Found {len(results)} results for query in {company}")
            return results
        except Exception as e:
            logger.error(f"Error searching documents for {company}: {str(e)}")
            return []
    
    def search_all_companies(self, query: str, k: int = 5) -> Dict[str, List[Document]]:
        """Search documents across all companies"""
        all_results = {}
        
        for company_key in self.config.companies.keys():
            results = self.search_documents(query, company_key, k)
            if results:
                all_results[company_key] = results
        
        return all_results
