"""
Batch Document Setup Utility for FinSight AI
Processes PDF documents with rate limit handling and batch processing
"""

import logging
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# Load environment variables
env_path = current_dir / ".env"
load_dotenv(env_path)

from config.config import get_system_config
from utils.document_processor import DocumentProcessor
from utils.llm_setup import test_llm_connection
from langchain_community.vectorstores import FAISS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchDocumentProcessor(DocumentProcessor):
    """Enhanced document processor with batch processing and rate limit handling"""
    
    def __init__(self, config=None, batch_size=50, delay_between_batches=2):
        super().__init__(config)
        self.batch_size = batch_size
        self.delay_between_batches = delay_between_batches
    
    def create_vector_store_with_batching(self, documents, company):
        """Create vector store with batch processing to handle rate limits"""
        if not documents:
            raise ValueError(f"No documents provided for {company}")
        
        logger.info(f"Creating vector store for {company} with {len(documents)} documents")
        logger.info(f"Using batch size: {self.batch_size}, delay: {self.delay_between_batches}s")
        
        # Process documents in batches
        all_vectors = None
        total_batches = (len(documents) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(documents), self.batch_size):
            batch_num = (i // self.batch_size) + 1
            batch_docs = documents[i:i + self.batch_size]
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch_docs)} documents)")
            
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                try:
                    if all_vectors is None:
                        # Create initial vector store
                        batch_vectors = FAISS.from_documents(batch_docs, self.embeddings)
                        all_vectors = batch_vectors
                    else:
                        # Add to existing vector store
                        batch_vectors = FAISS.from_documents(batch_docs, self.embeddings)
                        all_vectors.merge_from(batch_vectors)
                    
                    logger.info(f"✅ Batch {batch_num} completed successfully")
                    break
                    
                except Exception as e:
                    retry_count += 1
                    if "429" in str(e) or "rate limit" in str(e).lower():
                        wait_time = 60 * retry_count  # Exponential backoff
                        logger.warning(f"Rate limit hit. Waiting {wait_time}s before retry {retry_count}/{max_retries}")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"Error in batch {batch_num}: {str(e)}")
                        if retry_count >= max_retries:
                            raise e
                        time.sleep(5)
            
            # Small delay between batches to avoid rate limits
            if i + self.batch_size < len(documents):
                time.sleep(self.delay_between_batches)
        
        # Save vector store
        vector_path = self.config.vectors_dir / f"{company}_vectors"
        all_vectors.save_local(str(vector_path))
        
        logger.info(f"Vector store saved to: {vector_path}")
        return all_vectors

def main():
    """Main setup function with batch processing"""
    print("🎯 FinSight AI - Batch Document Setup Utility")
    print("=" * 50)
    
    try:
        # Initialize configuration
        config = get_system_config()
        print(f"✅ Configuration loaded")
        
        # Test LLM connection
        print("\n🔧 Testing LLM connection...")
        if test_llm_connection():
            print("✅ LLM connection successful")
        else:
            print("⚠️ LLM connection failed - check your API keys")
            return
        
        # Initialize batch document processor
        batch_size = int(input("Enter batch size (recommended: 25-50): ") or "25")
        delay = float(input("Enter delay between batches in seconds (recommended: 2-5): ") or "3")
        
        doc_processor = BatchDocumentProcessor(config, batch_size=batch_size, delay_between_batches=delay)
        print(f"✅ Batch document processor initialized (batch_size={batch_size}, delay={delay}s)")
        
        # Check document status
        print("\n📊 Checking document status...")
        stats = doc_processor.get_document_stats()
        
        companies_to_process = []
        for company_key, company_stats in stats.items():
            if company_stats['pdf_count'] > 0 and not company_stats['has_vectors']:
                companies_to_process.append(company_key)
                print(f"📋 {company_stats['display_name']}: {company_stats['pdf_count']} PDFs, needs processing")
        
        if not companies_to_process:
            print("✅ All companies already have vector indexes!")
            return
        
        # Process each company
        print(f"\n🔄 Processing {len(companies_to_process)} companies with batch processing...")
        
        for company_key in companies_to_process:
            company_name = config.companies[company_key].display_name
            
            print(f"\n📖 Processing {company_name} documents...")
            
            try:
                # Load documents
                documents = doc_processor.load_pdf_documents(company_key)
                
                if not documents:
                    print(f"  ⚠️ No documents loaded for {company_name}")
                    continue
                
                print(f"  ✅ Loaded {len(documents)} document chunks")
                
                # Create vector store with batching
                vector_store = doc_processor.create_vector_store_with_batching(documents, company_key)
                print(f"  ✅ Vector store created and saved")
                
                # Test the vector store
                test_results = vector_store.similarity_search("financial performance", k=2)
                print(f"  ✅ Vector store test successful ({len(test_results)} results)")
                
            except Exception as e:
                print(f"  ❌ Error processing {company_name}: {str(e)}")
                logger.error(f"Error processing {company_name}: {str(e)}")
        
        print("\n🎉 Batch processing complete!")
        print("\n🚀 You can now run the FinSight AI application:")
        print("   streamlit run app.py")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {str(e)}")
        logger.error(f"Setup failed: {str(e)}")

if __name__ == "__main__":
    main()
