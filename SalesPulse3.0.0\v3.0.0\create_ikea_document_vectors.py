"""
Create IKEA Document Vector Database
This script creates a new FAISS vector database from IKEA documents 
and replaces the old vector store in the retriever folder.
"""

import os
import sys
from pathlib import Path
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.embeddings.fastembed import FastEmbedEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from logger import logger

def create_ikea_document_vectors():
    """Create IKEA document vector database and replace the old one"""
    
    try:
        # Define paths
        base_dir = Path(__file__).parent
        documents_dir = base_dir / "data" / "documents"
        retriever_dir = base_dir / "data" / "retriever"
        
        logger.info(f"📁 Documents directory: {documents_dir}")
        logger.info(f"📁 Retriever directory: {retriever_dir}")
        
        # Check if documents directory exists
        if not documents_dir.exists():
            logger.error(f"❌ Documents directory not found: {documents_dir}")
            return False
            
        # Get all PDF files in documents directory
        pdf_files = list(documents_dir.glob("*.pdf"))
        if not pdf_files:
            logger.error(f"❌ No PDF files found in {documents_dir}")
            return False
            
        logger.info(f"📄 Found {len(pdf_files)} PDF files:")
        for pdf_file in pdf_files:
            logger.info(f"   - {pdf_file.name}")
        
        # Load all documents
        all_documents = []
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        
        for pdf_file in pdf_files:
            logger.info(f"📖 Loading: {pdf_file.name}")
            loader = PyPDFLoader(str(pdf_file))
            documents = loader.load()
            
            # Add metadata to identify the source document
            for doc in documents:
                doc.metadata['source_file'] = pdf_file.name
                doc.metadata['document_type'] = 'IKEA_Store_Document'
            
            # Split documents into chunks
            chunks = text_splitter.split_documents(documents)
            all_documents.extend(chunks)
            logger.info(f"   ✅ Loaded {len(chunks)} chunks from {pdf_file.name}")
        
        logger.info(f"📚 Total document chunks: {len(all_documents)}")
        
        if not all_documents:
            logger.error("❌ No documents were loaded")
            return False
        
        # Create embeddings
        logger.info("🔄 Creating embeddings...")
        embeddings = FastEmbedEmbeddings(model_name="BAAI/bge-base-en-v1.5")
        
        # Create vector store
        logger.info("🔄 Creating FAISS vector store...")
        vector_store = FAISS.from_documents(all_documents, embeddings)
        
        # Create retriever directory if it doesn't exist
        retriever_dir.mkdir(parents=True, exist_ok=True)
        
        # Backup old vector store if it exists
        old_index_path = retriever_dir / "index.faiss"
        old_pkl_path = retriever_dir / "index.pkl"
        
        if old_index_path.exists():
            backup_index = retriever_dir / "index_pizza_backup.faiss"
            old_index_path.rename(backup_index)
            logger.info(f"📦 Backed up old index to: {backup_index}")
            
        if old_pkl_path.exists():
            backup_pkl = retriever_dir / "index_pizza_backup.pkl"
            old_pkl_path.rename(backup_pkl)
            logger.info(f"📦 Backed up old pkl to: {backup_pkl}")
        
        # Save new vector store
        logger.info("💾 Saving new IKEA vector store...")
        vector_store.save_local(str(retriever_dir))
        
        logger.info("✅ Successfully created IKEA document vector database!")
        logger.info(f"📊 Total documents processed: {len(pdf_files)}")
        logger.info(f"📊 Total chunks created: {len(all_documents)}")
        logger.info(f"📁 Vector store saved to: {retriever_dir}")
        
        # Test the vector store
        logger.info("🧪 Testing vector store...")
        test_query = "IKEA store operations"
        results = vector_store.similarity_search(test_query, k=2)
        logger.info(f"✅ Test query returned {len(results)} results")
        for i, result in enumerate(results):
            logger.info(f"   Result {i+1}: {result.metadata.get('source_file', 'Unknown')} - {result.page_content[:100]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating IKEA vector database: {str(e)}")
        return False

def main():
    """Main execution function"""
    logger.info("🚀 Starting IKEA Document Vector Database Creation...")
    
    success = create_ikea_document_vectors()
    
    if success:
        logger.info("🎉 IKEA vector database creation completed successfully!")
        print("✅ IKEA vector database created and old vector store replaced!")
        print("📄 The system will now use IKEA documents for retrieval instead of pizza documents.")
    else:
        logger.error("💥 Failed to create IKEA vector database")
        print("❌ Failed to create IKEA vector database. Check logs for details.")

if __name__ == "__main__":
    main()
