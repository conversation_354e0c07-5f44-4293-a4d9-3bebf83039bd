"""
Chatbot Engine for FinSight AI
Handles query processing, document retrieval, and response generation
"""

import logging
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime

from langchain.schema import Document
from langchain.prompts import ChatPromptTemplate
from langchain.schema.messages import HumanMessage, AIMessage, SystemMessage

from config.config import get_system_config, ANALYSIS_PROMPTS
from utils.llm_setup import get_llm
from utils.document_processor import DocumentProcessor

logger = logging.getLogger(__name__)

class FinSightChatbot:
    """Main chatbot engine for FinSight AI"""
    
    def __init__(self):
        self.config = get_system_config()
        self.llm = get_llm()
        self.doc_processor = DocumentProcessor(self.config)
        self.conversation_history = []
    
    def _format_documents(self, documents: List[Document]) -> str:
        """Format retrieved documents for context"""
        if not documents:
            return "No relevant documents found."
        
        formatted_docs = []
        for i, doc in enumerate(documents, 1):
            company = doc.metadata.get('company_display', 'Unknown')
            source = doc.metadata.get('source_file', 'Unknown')
            
            formatted_docs.append(
                f"Document {i} (Source: {company} - {source}):\n"
                f"{doc.page_content}\n"
            )
        
        return "\n".join(formatted_docs)
    
    def _get_system_prompt(self, query_type: str = "general") -> str:
        """Get system prompt based on query type"""
        base_prompt = """
        You are FinSight AI, an expert financial analyst specializing in annual report analysis 
        for Castrol, Veedol, and Valvoline - three major companies in the lubricants industry.
        
        Your role is to:
        1. Analyze financial documents and provide accurate, data-driven insights
        2. Compare performance across companies when requested
        3. Identify trends, risks, and opportunities
        4. Provide specific numbers, percentages, and metrics when available
        5. Cite sources and be transparent about data limitations
        
        Guidelines:
        - Always base your analysis on the provided document context
        - Be specific and quantitative in your responses
        - Highlight key financial metrics and trends
        - When comparing companies, be objective and balanced
        - If information is not available in the context, clearly state this
        - Use professional financial analysis language
        """
        
        if query_type in ANALYSIS_PROMPTS:
            return base_prompt + "\n\n" + ANALYSIS_PROMPTS[query_type]
        
        return base_prompt
    
    def _classify_query(self, query: str) -> str:
        """Classify the type of query to use appropriate prompts"""
        query_lower = query.lower()
        
        if any(term in query_lower for term in ['revenue', 'profit', 'financial', 'earnings', 'income']):
            return 'financial_performance'
        elif any(term in query_lower for term in ['strategy', 'strategic', 'plan', 'future', 'growth']):
            return 'business_strategy'
        elif any(term in query_lower for term in ['risk', 'threat', 'challenge', 'uncertainty']):
            return 'risk_analysis'
        elif any(term in query_lower for term in ['sustainability', 'esg', 'environment', 'social', 'governance']):
            return 'sustainability'
        elif any(term in query_lower for term in ['compare', 'comparison', 'versus', 'vs', 'against']):
            return 'comparative'
        else:
            return 'general'
    
    def _extract_companies_from_query(self, query: str) -> List[str]:
        """Extract company names mentioned in the query"""
        query_lower = query.lower()
        mentioned_companies = []
        
        for company_key, company_config in self.config.companies.items():
            if company_config.display_name.lower() in query_lower or company_key in query_lower:
                mentioned_companies.append(company_key)
        
        # If no specific companies mentioned, include all
        if not mentioned_companies:
            mentioned_companies = list(self.config.companies.keys())
        
        return mentioned_companies
    
    def retrieve_relevant_documents(self, query: str, companies: List[str], k: int = 5) -> Dict[str, List[Document]]:
        """Retrieve relevant documents for the query"""
        all_results = {}
        
        for company in companies:
            results = self.doc_processor.search_documents(query, company, k)
            if results:
                all_results[company] = results
        
        return all_results
    
    def generate_response(self, query: str, chat_history: List[Dict] = None) -> Dict[str, Any]:
        """Generate response to user query"""
        try:
            # Classify query and extract companies
            query_type = self._classify_query(query)
            companies = self._extract_companies_from_query(query)
            
            logger.info(f"Processing query type: {query_type}, companies: {companies}")
            
            # Retrieve relevant documents
            retrieved_docs = self.retrieve_relevant_documents(
                query, companies, self.config.model_config.top_k_retrieval
            )
            
            if not retrieved_docs:
                return {
                    'response': "I couldn't find any relevant documents to answer your question. Please ensure that annual reports have been uploaded for the companies you're asking about.",
                    'sources': [],
                    'companies_analyzed': companies,
                    'query_type': query_type
                }
            
            # Format context from retrieved documents
            context_parts = []
            sources = []
            
            for company, docs in retrieved_docs.items():
                company_display = self.config.companies[company].display_name
                context_parts.append(f"\n=== {company_display} Documents ===")
                context_parts.append(self._format_documents(docs))
                
                # Collect sources
                for doc in docs:
                    source_info = {
                        'company': company_display,
                        'file': doc.metadata.get('source_file', 'Unknown'),
                        'page': doc.metadata.get('page', 'Unknown')
                    }
                    if source_info not in sources:
                        sources.append(source_info)
            
            context = "\n".join(context_parts)
            
            # Create prompt
            system_prompt = self._get_system_prompt(query_type)
            
            prompt_template = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                ("human", """
                Based on the following context from annual reports, please answer the user's question:
                
                Context:
                {context}
                
                Question: {question}
                
                Please provide a comprehensive analysis based on the available information.
                """)
            ])
            
            # Generate response
            formatted_prompt = prompt_template.format(context=context, question=query)
            response = self.llm.invoke(formatted_prompt)
            
            # Store in conversation history
            self.conversation_history.append({
                'timestamp': datetime.now().isoformat(),
                'query': query,
                'response': response.content,
                'query_type': query_type,
                'companies': companies,
                'sources': sources
            })
            
            return {
                'response': response.content,
                'sources': sources,
                'companies_analyzed': companies,
                'query_type': query_type,
                'retrieved_docs_count': sum(len(docs) for docs in retrieved_docs.values())
            }
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                'response': f"I encountered an error while processing your question: {str(e)}. Please try again or rephrase your question.",
                'sources': [],
                'companies_analyzed': [],
                'query_type': 'error'
            }
    
    def get_conversation_history(self) -> List[Dict]:
        """Get conversation history"""
        return self.conversation_history
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        logger.info("Conversation history cleared")
    
    def get_available_companies(self) -> Dict[str, Dict]:
        """Get information about available companies and their documents"""
        return self.doc_processor.get_document_stats()
    
    def refresh_document_index(self, company: str = None) -> Dict[str, bool]:
        """Refresh document index for specific company or all companies"""
        results = {}
        
        if company:
            results[company] = self.doc_processor.refresh_vector_store(company)
        else:
            for company_key in self.config.companies.keys():
                results[company_key] = self.doc_processor.refresh_vector_store(company_key)
        
        return results
