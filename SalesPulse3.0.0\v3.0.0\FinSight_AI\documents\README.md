# 📁 Document Storage Guide

This directory contains the annual reports for analysis by FinSight AI. Each company has its own subdirectory where you should place the corresponding PDF annual reports.

## 📂 Directory Structure

```
documents/
├── castrol/          # Castrol annual reports
├── veedol/           # Veedol annual reports
└── valvoline/        # Valvoline annual reports
```

## 📄 Document Requirements

### Supported Formats
- **PDF files only** (`.pdf` extension)
- Annual reports, quarterly reports, financial statements
- Any financial documents from the three companies

### File Naming Conventions (Recommended)
- `Castrol_Annual_Report_2023.pdf`
- `Veedol_Annual_Report_2023.pdf`
- `Valvoline_Annual_Report_2023.pdf`
- `Castrol_Q4_2023.pdf`

### File Size Considerations
- No strict size limits, but larger files take longer to process
- Typical annual reports (50-200 pages) process efficiently
- Very large files (>500 pages) may require additional processing time

## 🚀 Getting Started

1. **Download Annual Reports**
   - Visit the official websites of Castrol, Veedol, and Valvoline
   - Download their latest annual reports in PDF format
   - Save them to the appropriate company directories

2. **Place Documents**
   ```
   documents/castrol/Castrol_Annual_Report_2023.pdf
   documents/veedol/Veedol_Annual_Report_2023.pdf
   documents/valvoline/Valvoline_Annual_Report_2023.pdf
   ```

3. **Process Documents**
   ```bash
   python setup_documents.py
   ```

4. **Start Analysis**
   ```bash
   streamlit run app.py
   ```

## 📊 Company Information

### 🟢 Castrol
- **Industry**: Automotive and industrial lubricants
- **Parent Company**: BP plc
- **Website**: [castrol.com](https://www.castrol.com)
- **Investor Relations**: Look for annual reports in their investor section

### 🟠 Veedol
- **Industry**: Premium motor oils and lubricants
- **Parent Company**: Tide Water Oil Company
- **Website**: [veedol.com](https://www.veedol.com)
- **Reports**: Check their corporate/investor pages

### 🔴 Valvoline
- **Industry**: Automotive lubricants and services
- **Stock**: NYSE: VVV
- **Website**: [valvoline.com](https://www.valvoline.com)
- **Investor Relations**: SEC filings and annual reports available

## 🔄 Document Updates

### Adding New Documents
1. Place new PDF files in the appropriate company directory
2. Run `python setup_documents.py` to reprocess
3. Or use the "Refresh All Indexes" button in the app

### Replacing Documents
1. Remove old PDF files
2. Add new PDF files
3. Refresh the document indexes

### Multiple Years
- You can add multiple years of reports for each company
- The system will analyze all available documents
- Useful for trend analysis and year-over-year comparisons

## ⚠️ Important Notes

- **File Permissions**: Ensure PDF files are readable
- **File Integrity**: Corrupted PDFs may cause processing errors
- **Content Quality**: Text-based PDFs work best (not scanned images)
- **Language**: System optimized for English-language reports
- **Updates**: Remember to refresh indexes after adding new documents

## 🔍 Troubleshooting

### No Documents Found
- Check file extensions (must be `.pdf`)
- Verify files are in correct company directories
- Check file permissions

### Processing Errors
- Ensure PDFs are not password-protected
- Try with smaller PDF files first
- Check the logs for specific error messages

### Poor Analysis Quality
- Ensure PDFs contain searchable text (not just images)
- Use official annual reports rather than summaries
- Include multiple years for better trend analysis

---

**Ready to analyze?** Add your PDF documents and run `python setup_documents.py` to get started!
