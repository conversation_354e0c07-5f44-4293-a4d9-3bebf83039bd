"""
Create IKEA Store Information Documents for RAG System
"""
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
import os

def create_ikea_documents():
    """Create comprehensive IKEA store documents for RAG system"""
    
    # Create documents directory if it doesn't exist
    doc_dir = "data/documents"
    os.makedirs(doc_dir, exist_ok=True)
    
    # Document 1: IKEA Store Operations and Services
    create_store_operations_doc(doc_dir)
    
    # Document 2: IKEA Food & Beverages Information
    create_food_beverages_doc(doc_dir)
    
    # Document 3: IKEA Store Performance Guidelines
    create_performance_guidelines_doc(doc_dir)
    
    print("✅ All IKEA documents created successfully!")

def create_store_operations_doc(doc_dir):
    """Create IKEA Store Operations and Services document"""
    
    doc_path = os.path.join(doc_dir, "IKEA_Store_Operations.pdf")
    doc = SimpleDocTemplate(doc_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=20,
        alignment=TA_CENTER
    )
    
    header_style = ParagraphStyle(
        'CustomHeader',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor='blue'
    )
    
    content = []
    
    # Title
    content.append(Paragraph("IKEA Store Operations & Services", title_style))
    content.append(Spacer(1, 20))
    
    # Store Information
    content.append(Paragraph("Store Locations in UAE", header_style))
    store_info = """
    <b>DJA Store (Dubai Festival City)</b><br/>
    Location: Dubai Festival City Mall, Dubai, UAE<br/>
    Size: Large format store with full product range<br/>
    Features: Complete home furnishing solutions, restaurant, customer parking<br/>
    Operating Hours: 10:00 AM - 10:00 PM (Sunday-Wednesday), 10:00 AM - 12:00 AM (Thursday-Saturday)<br/>
    Special Services: Home delivery, assembly service, interior design consultation<br/><br/>
    
    <b>YAS Store (Abu Dhabi)</b><br/>
    Location: Yas Mall, Yas Island, Abu Dhabi, UAE<br/>
    Size: Medium format store with curated product selection<br/>
    Features: Focused on small furniture and home accessories, compact restaurant<br/>
    Operating Hours: 10:00 AM - 10:00 PM (Sunday-Wednesday), 10:00 AM - 12:00 AM (Thursday-Saturday)<br/>
    Special Services: Click & collect, personal shopping assistance, design workshops<br/>
    """
    content.append(Paragraph(store_info, styles['Normal']))
    content.append(Spacer(1, 15))
    
    # Channel Information
    content.append(Paragraph("Sales Channels", header_style))
    channel_info = """
    <b>Store Channel:</b><br/>
    The primary retail channel offering the complete IKEA experience including:
    • Full product range display and sales
    • Self-service warehouse areas
    • Customer service and information desks
    • Checkout and payment processing
    • Product pickup and loading services<br/><br/>
    
    <b>IFB Channel (IKEA Food & Beverages):</b><br/>
    Integrated restaurant and food retail operations providing:
    • Swedish and international cuisine
    • IKEA signature food products
    • Grab-and-go food items
    • Beverages and snacks
    • Take-away food services
    """
    content.append(Paragraph(channel_info, styles['Normal']))
    content.append(Spacer(1, 15))
    
    # Customer Services
    content.append(Paragraph("Customer Services", header_style))
    services_info = """
    <b>Shopping Services:</b>
    • Personal shopping assistance and product recommendations
    • Interior design consultation and room planning
    • Product availability checking and reservations
    • Home delivery and installation services
    • Assembly service for furniture and larger items<br/><br/>
    
    <b>Customer Support:</b>
    • Product information and specifications
    • Warranty and return policy assistance
    • Spare parts ordering and replacement
    • Customer feedback and complaint resolution
    • IKEA Family membership benefits and loyalty programs<br/><br/>
    
    <b>Digital Services:</b>
    • IKEA app for product browsing and store navigation
    • Click & collect service for online orders
    • Virtual room planning tools
    • Augmented reality product placement
    • Digital receipts and purchase history
    """
    content.append(Paragraph(services_info, styles['Normal']))
    
    doc.build(content)
    print(f"Created: {doc_path}")

def create_food_beverages_doc(doc_dir):
    """Create IKEA Food & Beverages Information document"""
    
    doc_path = os.path.join(doc_dir, "IKEA_Food_Beverages.pdf")
    doc = SimpleDocTemplate(doc_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=20,
        alignment=TA_CENTER
    )
    
    header_style = ParagraphStyle(
        'CustomHeader',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor='blue'
    )
    
    content = []
    
    # Title
    content.append(Paragraph("IKEA Food & Beverages (IFB)", title_style))
    content.append(Spacer(1, 20))
    
    # Restaurant Menu
    content.append(Paragraph("Restaurant Menu & Offerings", header_style))
    menu_info = """
    <b>Signature Swedish Dishes:</b><br/>
    • Swedish Meatballs (Köttbullar) - Traditional recipe with cream sauce and lingonberry
    • Vegetable Balls (Grönsaksbullar) - Plant-based alternative made with vegetables
    • Salmon Fillet - Grilled with seasonal vegetables and hollandaise sauce
    • Chicken Schnitzel - Crispy breaded chicken with potato salad
    • Fish & Chips - Traditional battered fish with crispy fries<br/><br/>
    
    <b>International Cuisine:</b><br/>
    • Middle Eastern Specialties - Hummus, falafel, and grilled chicken
    • Asian Fusion - Stir-fried noodles, rice bowls, and curry dishes
    • Italian Favorites - Pasta dishes, pizza slices, and bruschetta
    • Healthy Options - Salad bar, quinoa bowls, and fresh fruit
    • Kids Menu - Mini portions and child-friendly options<br/><br/>
    
    <b>Beverages:</b><br/>
    • Hot Beverages - Coffee, tea, hot chocolate, and specialty drinks
    • Cold Beverages - Soft drinks, juices, smoothies, and flavored water
    • Swedish Specialties - Lingonberry juice, elderflower drink
    • Healthy Options - Fresh juices, protein shakes, and organic teas
    """
    content.append(Paragraph(menu_info, styles['Normal']))
    content.append(Spacer(1, 15))
    
    # Food Retail
    content.append(Paragraph("Food Retail Products", header_style))
    retail_info = """
    <b>IKEA Food Market:</b><br/>
    • Swedish Food Specialties - Frozen meatballs, fish roe, crispbread
    • International Snacks - Cookies, chocolates, and confectionery
    • Cooking Ingredients - Sauces, spices, and recipe kits
    • Frozen Foods - Ready meals, desserts, and ice cream
    • Beverages - Juices, sodas, and specialty drinks<br/><br/>
    
    <b>Grab & Go Options:</b><br/>
    • Fresh Sandwiches - Made daily with quality ingredients
    • Salad Boxes - Healthy pre-packaged salads and protein bowls
    • Bakery Items - Pastries, muffins, and fresh bread
    • Snack Packs - Nuts, fruits, and energy bars
    • Ready-to-eat Meals - Hot meals for immediate consumption
    """
    content.append(Paragraph(retail_info, styles['Normal']))
    content.append(Spacer(1, 15))
    
    # Dietary Information
    content.append(Paragraph("Dietary Options & Allergen Information", header_style))
    dietary_info = """
    <b>Special Dietary Requirements:</b><br/>
    • Vegetarian Options - Clearly marked plant-based dishes
    • Vegan Choices - Completely plant-based meals and snacks
    • Gluten-Free - Specially prepared options for celiac customers
    • Halal Certified - Selected menu items meeting halal standards
    • Low-Calorie - Health-conscious options with nutritional information<br/><br/>
    
    <b>Allergen Management:</b><br/>
    • Clear labeling of all major allergens in menu items
    • Staff training on allergen awareness and customer assistance
    • Separate preparation areas for allergen-free options
    • Detailed ingredient lists available upon request
    • Emergency procedures for allergic reactions
    """
    content.append(Paragraph(dietary_info, styles['Normal']))
    
    doc.build(content)
    print(f"Created: {doc_path}")

def create_performance_guidelines_doc(doc_dir):
    """Create IKEA Store Performance Guidelines document"""
    
    doc_path = os.path.join(doc_dir, "IKEA_Performance_Guidelines.pdf")
    doc = SimpleDocTemplate(doc_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=20,
        alignment=TA_CENTER
    )
    
    header_style = ParagraphStyle(
        'CustomHeader',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor='blue'
    )
    
    content = []
    
    # Title
    content.append(Paragraph("IKEA Store Performance Guidelines", title_style))
    content.append(Spacer(1, 20))
    
    # KPI Definitions
    content.append(Paragraph("Key Performance Indicators (KPIs)", header_style))
    kpi_info = """
    <b>Financial Metrics:</b><br/>
    • <b>Actuals (Act):</b> Net sales amount in AED - Primary revenue metric
    • <b>Last Year (Ly):</b> Previous year net sales for comparison
    • <b>Year-over-Year Growth (vs_Ly_percent):</b> Percentage change from last year
    • <b>Average Transaction Value (ATV):</b> Average spend per customer transaction<br/><br/>
    
    <b>Customer Metrics:</b><br/>
    • <b>Visitors:</b> Total footfall count entering the store
    • <b>Customers:</b> Number of completed transactions
    • <b>Conversion Rate:</b> Percentage of visitors who make a purchase
    • <b>Items per Customer:</b> Average number of items purchased per transaction<br/><br/>
    
    <b>Product Metrics:</b><br/>
    • <b>Items Sold:</b> Total quantity of products sold
    • <b>Price per Item:</b> Average selling price per individual item
    • <b>Product Mix:</b> Distribution of sales across different product categories
    """
    content.append(Paragraph(kpi_info, styles['Normal']))
    content.append(Spacer(1, 15))
    
    # Performance Benchmarks
    content.append(Paragraph("Performance Benchmarks & Targets", header_style))
    benchmark_info = """
    <b>Conversion Rate Targets:</b><br/>
    • Store Channel: 25-35% (Industry leading performance)
    • IFB Channel: 8-12% (Food service industry standard)
    • Peak Hours: 40-50% conversion during weekend afternoons
    • Seasonal Events: 60%+ during major sales periods<br/><br/>
    
    <b>Average Transaction Value Goals:</b><br/>
    • Store Channel: 400-600 AED per transaction
    • IFB Channel: 80-150 AED per transaction
    • Premium Product Focus: 15-20% increase in ATV
    • Cross-selling Success: 3-5 items per customer average<br/><br/>
    
    <b>Year-over-Year Growth Expectations:</b><br/>
    • Healthy Growth: 5-15% increase over previous year
    • Market Expansion: New product launches driving 20%+ growth
    • Seasonal Adjustments: Account for holiday and cultural events
    • Economic Factors: Consider market conditions and consumer spending
    """
    content.append(Paragraph(benchmark_info, styles['Normal']))
    content.append(Spacer(1, 15))
    
    # Operational Excellence
    content.append(Paragraph("Operational Excellence Guidelines", header_style))
    operations_info = """
    <b>Customer Experience Standards:</b><br/>
    • Store Layout: Clear navigation and product discovery
    • Staff Assistance: Knowledgeable and helpful customer service
    • Queue Management: Efficient checkout and minimal wait times
    • Product Availability: 95%+ in-stock rate for core products
    • Cleanliness: Maintain high hygiene standards throughout store<br/><br/>
    
    <b>Inventory Management:</b><br/>
    • Stock Rotation: First-in-first-out for food products
    • Demand Forecasting: Data-driven inventory planning
    • Seasonal Preparation: Anticipate holiday and event demand
    • Supply Chain Coordination: Minimize stockouts and overstock
    • Quality Control: Regular product quality checks and maintenance<br/><br/>
    
    <b>Performance Monitoring:</b><br/>
    • Daily Reporting: Track KPIs and performance metrics
    • Weekly Reviews: Analyze trends and identify improvement areas
    • Monthly Analysis: Compare against targets and previous periods
    • Quarterly Planning: Strategic adjustments and goal setting
    • Annual Evaluation: Comprehensive performance assessment
    """
    content.append(Paragraph(operations_info, styles['Normal']))
    
    doc.build(content)
    print(f"Created: {doc_path}")

if __name__ == "__main__":
    try:
        create_ikea_documents()
    except ImportError as e:
        print(f"❌ Error: Required library not installed: {e}")
        print("Please install reportlab: pip install reportlab")
    except Exception as e:
        print(f"❌ Error creating documents: {e}")
