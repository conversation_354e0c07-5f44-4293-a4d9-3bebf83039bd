# SalesPulse Assistant.py - Technical Flow Diagram

## 📊 Complete System Architecture & Flow

```mermaid
graph TB
    %% External Dependencies
    subgraph ExtLib ["External Libraries"]
        ST[Streamlit]
        PIL[PIL/Pillow]
        PD[Pandas]
        OS[OS Module]
        RE[Regex]
        HASH[Hashlib]
        TEMP[Tempfile]
        SHUTIL[Shutil]
        TIME[Time]
    end

    %% Internal Modules
    subgraph IntMod ["Internal Modules"]
        CONST[constants.constant]
        TOOLS[tools.tools_agents]
        DATA[data.dataloader]
        CONFIG[configuration.config]
        MAIN[main.py]
        UTILS[utils.utils]
        LOGGER[logger]
        REPL[langchain_experimental.utilities.PythonREPL]
    end

    %% Application Entry Point
    ENTRY[assistant.py Main Entry Point] --> MAIN_FUNC[main function]

    %% Application Initialization
    MAIN_FUNC --> INIT_CSS[Apply CUSTOM_CSS]
    MAIN_FUNC --> INIT_STATE[initialize_session_state]
    MAIN_FUNC --> PAGE_ROUTER{Page Router}

    %% Session State Management
    INIT_STATE --> STATE_VARS[Session Variables:<br/>page login/chat<br/>messages array<br/>authenticated<br/>username<br/>login_attempts<br/>session_id<br/>generated_plots array]

    %% Page Routing Logic
    PAGE_ROUTER -->|Not Authenticated| LOGIN_PAGE[render_login_page]
    PAGE_ROUTER -->|Authenticated| CHAT_PAGE[render_chat_page]

    %% Login Page Components
    subgraph LoginFlow ["Login Flow"]
        LOGIN_PAGE --> LOGO_DISPLAY[Display Nihilent Logo]
        LOGIN_PAGE --> LOGIN_FORM[Login Form UI]
        LOGIN_FORM --> AUTH_CHECK[authenticate function]
        AUTH_CHECK --> CRED_VALID{Valid Credentials?}
        CRED_VALID -->|Yes| SET_AUTH[Set Authenticated State]
        CRED_VALID -->|No| LOGIN_ERROR[Show Error Message]
        SET_AUTH --> REDIRECT_CHAT[Redirect to Chat Page]
    end

    %% Chat Page Components
    subgraph ChatFlow ["Chat Application Flow"]
        CHAT_PAGE --> TEMP_SETUP[Temporary Directory Setup]
        CHAT_PAGE --> SIDEBAR_UI[Render Sidebar UI]
        CHAT_PAGE --> CHAT_HISTORY[Display Chat History]
        CHAT_PAGE --> CHAT_INPUT[Chat Input Handler]

        %% Temporary Directory Management
        TEMP_SETUP --> HASH_SESSION[Create Session Hash:<br/>SHA256 username + session_id]
        TEMP_SETUP --> CREATE_TEMP[Create Artifact Directory:<br/>tmp/hash/]

        %% Sidebar Components
        SIDEBAR_UI --> LOGO_SIDE[Nihilent Logo Display]
        SIDEBAR_UI --> USER_INFO[User Welcome Message]
        SIDEBAR_UI --> REFRESH_BTN[Refresh Chat Button]
        SIDEBAR_UI --> LOGOUT_BTN[Logout Button]
        REFRESH_BTN --> REFRESH_FUNC[refresh_chat function]
        LOGOUT_BTN --> LOGOUT_FUNC[logout function]
        REFRESH_FUNC --> CLEAR_TEMP1[clear_temp artifact_dir]
        LOGOUT_FUNC --> CLEAR_TEMP2[clear_temp artifact_dir]

        %% Chat Processing
        CHAT_INPUT --> PROMPT_RECEIVED{User Prompt Received?}
        PROMPT_RECEIVED -->|Yes| ADD_USER_MSG[Add User Message to History]
        ADD_USER_MSG --> PREP_HISTORY[Prepare Chat History<br/>Last 3 messages]
        PREP_HISTORY --> SPECIAL_CHECK{Special Command?<br/>generate dashboard}
        
        %% Special Dashboard Generation
        SPECIAL_CHECK -->|Yes| DASH_GEN[Dashboard Generation]
        DASH_GEN --> PLOT_CHECK{Generated Plots Exist?}
        PLOT_CHECK -->|Yes| EXEC_PLOTS[Execute Plot Generation Code]
        EXEC_PLOTS --> PROGRESS_BAR[Show Progress Bar]
        
        %% Normal Query Processing
        SPECIAL_CHECK -->|No| PROCESS_QUERY[process_query prompt chat_history]
    end

    %% Query Processing Flow main.py integration
    subgraph QueryPipe ["Query Processing Pipeline"]
        PROCESS_QUERY --> REFORM_Q[Reform Question using LLM]
        REFORM_Q --> AGENT_INVOKE[agent_executor.invoke]
        AGENT_INVOKE --> FALLBACK_CHECK{Response Indicates Fallback?}
        FALLBACK_CHECK -->|Yes| FALLBACK_AGENT[fallback_agent.invoke]
        FALLBACK_CHECK -->|No| PROCESS_RESPONSE[Process Agent Response]
        FALLBACK_AGENT --> PROCESS_RESPONSE
    end

    %% Response Processing
    subgraph RespProcess ["Response Processing"]
        PROCESS_RESPONSE --> EXTRACT_RESULT[Extract Result from Response]
        EXTRACT_RESULT --> REMOVE_MD_IMAGES[remove_markdown_images]
        REMOVE_MD_IMAGES --> GET_TOOLS[Extract Tool Usage from Steps]
        GET_TOOLS --> TOOL_HANDLER{Tool Used?}
        
        %% Tool-specific Processing
        TOOL_HANDLER -->|query_documents| DOC_SEARCH[Vector Similarity Search]
        TOOL_HANDLER -->|python_repl| PLOT_CODE[Extract Plotting Code]
        TOOL_HANDLER -->|Other| DISPLAY_RESULT[Display Result]
        
        DOC_SEARCH --> CREATE_CITATIONS[Create Citations with URLs]
        PLOT_CODE --> CHECK_CHARTS[check_for_charts]
        CHECK_CHARTS -->|Has Charts| UPDATE_PLOT_CODE[Update Code for Saving]
        CHECK_CHARTS -->|No Charts| DISPLAY_RESULT
        UPDATE_PLOT_CODE --> SAVE_PLOT_STATE[Add to generated_plots]
        
        CREATE_CITATIONS --> DISPLAY_WITH_SOURCES[Display Result + Citations]
        SAVE_PLOT_STATE --> DISPLAY_RESULT
        DISPLAY_WITH_SOURCES --> ADD_ASSISTANT_MSG[Add Assistant Message to History]
        DISPLAY_RESULT --> ADD_ASSISTANT_MSG
    end

    %% Error Handling
    subgraph ErrorHandle ["Error Handling"]
        PROCESS_QUERY -.->|Exception| ERROR_HANDLER[Exception Handler]
        ERROR_HANDLER --> FAIL_MESSAGE[Display Failure Message]
        FAIL_MESSAGE --> ADD_ERROR_MSG[Add Error Message to History]
    end

    %% Utility Functions
    subgraph UtilFunc ["Utility Functions"]
        GENERATE_SID[generate_session_id]
        IMAGE_B64[image_to_base64]
        CLEAR_TEMP_FUNC[clear_temp dirname]
        REMOVE_MD[remove_markdown_images]
        CREATE_CITE[create_citations file_path]
    end

    %% Data Flow Connections
    CONST --> LOGIN_PAGE
    CONST --> CHAT_PAGE
    PIL --> IMAGE_B64
    UTILS --> CREATE_CITE
    UTILS --> REMOVE_MD
    DATA --> DOC_SEARCH
    TOOLS --> AGENT_INVOKE
    MAIN --> PROCESS_QUERY
    LOGGER --> PROCESS_QUERY
    REPL --> EXEC_PLOTS

    %% Styling
    classDef entryPoint fill:#ff6b6b,stroke:#d63031,stroke-width:3px,color:#fff
    classDef pageFlow fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef processing fill:#00b894,stroke:#00a085,stroke-width:2px,color:#fff
    classDef utility fill:#fdcb6e,stroke:#e17055,stroke-width:2px,color:#333
    classDef error fill:#e84393,stroke:#d63031,stroke-width:2px,color:#fff
    classDef external fill:#a29bfe,stroke:#6c5ce7,stroke-width:2px,color:#fff

    class ENTRY entryPoint
    class LOGIN_PAGE,CHAT_PAGE,MAIN_FUNC pageFlow
    class PROCESS_QUERY,AGENT_INVOKE,REFORM_Q processing
    class GENERATE_SID,IMAGE_B64,CLEAR_TEMP_FUNC utility
    class ERROR_HANDLER,FAIL_MESSAGE error
    class ST,PIL,PD,OS external
```

## 🔧 Key Components Breakdown

### 1. **Application Entry & Configuration**
```python
# Core Streamlit Configuration
st.set_page_config(
    page_title="Sales Pulse",
    page_icon="🥚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Environment Setup
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''
```

### 2. **Session State Management**
```python
def initialize_session_state():
    # Authentication State
    - page: "login" | "chat"
    - authenticated: bool
    - username: str
    - login_attempts: int
    - session_id: int
    
    # Chat State  
    - messages: List[Dict[str, str]]
    - generated_plots: List[str]
```

### 3. **Authentication Flow**
```python
def authenticate(username, password) -> bool:
    valid_credentials = {
        "Admin": "admin123",
        "Jane Doe": "user123", 
        "John Doe": "user123",
        "Demo User": "demo123"
    }
    # Validates credentials and sets session state
```

### 4. **Temporary File Management**
```python
# Session-based temporary directory creation
hex_salt = f"{username}_{session_id}"
hash_hex = hashlib.sha256(hex_salt.encode()).hexdigest()
artifact_dir = os.path.join('./.tmp', hash_hex)
```

### 5. **Query Processing Pipeline**
```python
# Main processing flow:
1. reform_question(prompt, chat_history) → reformed_question
2. agent_executor.invoke() → response  
3. fallback_detection() → fallback_agent if needed
4. tool_extraction() → identify used tools
5. post_processing() → citations, plot handling
```

### 6. **Tool Integration Points**

#### SQL Tool
- **Function**: `create_retriver_for_sql`
- **Purpose**: Generate and execute SQL queries
- **Output**: Database results

#### Python REPL Tool  
- **Function**: `run_python_code`
- **Purpose**: Execute Python code for data analysis
- **Chart Detection**: `check_for_charts()`
- **Plot Saving**: Updates code to save to artifact directory

#### Document Retriever Tool
- **Function**: Vector similarity search
- **Purpose**: Find relevant documents
- **Citations**: Creates PDF citations with URLs

### 7. **Error Handling Strategy**
```python
try:
    # Main processing
    answer = process_query(prompt, chat_history)
except Exception as e:
    # Fallback error message
    fail_note = "Apologies 😔, I couldn't process your request..."
    # Log error and display user-friendly message
```

### 8. **Dashboard Generation Feature**
```python
if prompt.lower().strip() == "generate dashboard":
    # Execute all stored plotting codes
    for plot in st.session_state.generated_plots:
        repl.run(repl.sanitize_input(plot))
```

## 📋 File Dependencies & Import Structure

### External Dependencies
- **streamlit**: UI framework
- **PIL (Pillow)**: Image processing  
- **pandas**: Data manipulation
- **hashlib**: Session hashing
- **tempfile/shutil**: File management
- **os/time/re**: System utilities

### Internal Module Dependencies
- **constants.constant**: UI styling, database paths
- **tools.tools_agents**: AI agent configuration
- **data.dataloader**: Vector database, model loading
- **configuration.config**: Session configuration  
- **main.py**: Core query processing logic
- **utils.utils**: Utility functions (citations, image processing)
- **logger**: Logging functionality

## 🔄 Data Flow Summary

1. **User Input** → Chat input field
2. **Authentication** → Session state management  
3. **Query Processing** → main.process_query()
4. **Agent Execution** → tools.tools_agents
5. **Tool Usage** → SQL/Python REPL/Document retrieval
6. **Response Processing** → Citations, plot handling
7. **UI Update** → Display results, update chat history
8. **State Persistence** → Session state, temporary files

This architecture provides a robust, multi-modal AI assistant with comprehensive error handling, session management, and integrated data processing capabilities.
