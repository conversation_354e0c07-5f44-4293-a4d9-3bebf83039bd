"""
Document Setup Utility for FinSight AI
Processes PDF documents and creates vector indexes
"""

import logging
import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from config.config import get_system_config
from utils.document_processor import DocumentProcessor
from utils.llm_setup import test_llm_connection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main setup function"""
    print("🎯 FinSight AI - Document Setup Utility")
    print("=" * 50)
    
    try:
        # Initialize configuration
        config = get_system_config()
        print(f"✅ Configuration loaded")
        print(f"📁 Base directory: {config.base_dir}")
        
        # Test LLM connection
        print("\n🔧 Testing LLM connection...")
        if test_llm_connection():
            print("✅ LLM connection successful")
        else:
            print("⚠️ LLM connection failed - check your API keys")
            return
        
        # Initialize document processor
        doc_processor = DocumentProcessor(config)
        print("✅ Document processor initialized")
        
        # Check document status
        print("\n📊 Checking document status...")
        stats = doc_processor.get_document_stats()
        
        total_docs = 0
        companies_with_docs = []
        
        for company_key, company_stats in stats.items():
            company_name = company_stats['display_name']
            doc_count = company_stats['pdf_count']
            has_vectors = company_stats['has_vectors']
            
            print(f"\n{company_name}:")
            print(f"  📄 PDF files: {doc_count}")
            print(f"  🔍 Vector index: {'✅' if has_vectors else '❌'}")
            
            if doc_count > 0:
                companies_with_docs.append(company_key)
                total_docs += doc_count
                
                print(f"  📋 Files:")
                for file in company_stats['pdf_files']:
                    print(f"    - {file}")
        
        if total_docs == 0:
            print("\n⚠️ No PDF documents found!")
            print("Please add PDF annual reports to the following directories:")
            for company_key, company_config in config.companies.items():
                company_dir = config.documents_dir / company_key
                print(f"  - {company_config.display_name}: {company_dir}")
            return
        
        print(f"\n📈 Total documents found: {total_docs}")
        print(f"📈 Companies with documents: {len(companies_with_docs)}")
        
        # Ask user if they want to process documents
        print("\n" + "=" * 50)
        response = input("Do you want to process documents and create vector indexes? (y/n): ")
        
        if response.lower() not in ['y', 'yes']:
            print("Setup cancelled.")
            return
        
        # Process documents for each company
        print("\n🔄 Processing documents...")
        
        success_count = 0
        for company_key in companies_with_docs:
            company_name = config.companies[company_key].display_name
            
            print(f"\n📖 Processing {company_name} documents...")
            
            try:
                # Load documents
                documents = doc_processor.load_pdf_documents(company_key)
                
                if not documents:
                    print(f"  ⚠️ No documents loaded for {company_name}")
                    continue
                
                print(f"  ✅ Loaded {len(documents)} document chunks")
                
                # Create vector store
                vector_store = doc_processor.create_vector_store(documents, company_key)
                print(f"  ✅ Vector store created and saved")
                
                # Test the vector store
                test_results = vector_store.similarity_search("financial performance", k=2)
                print(f"  ✅ Vector store test successful ({len(test_results)} results)")
                
                success_count += 1
                
            except Exception as e:
                print(f"  ❌ Error processing {company_name}: {str(e)}")
                logger.error(f"Error processing {company_name}: {str(e)}")
        
        # Summary
        print("\n" + "=" * 50)
        print("🎉 Setup Complete!")
        print(f"✅ Successfully processed: {success_count}/{len(companies_with_docs)} companies")
        
        if success_count > 0:
            print("\n🚀 You can now run the FinSight AI application:")
            print("   streamlit run app.py")
        else:
            print("\n⚠️ No companies were successfully processed.")
            print("Please check the error messages above and try again.")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {str(e)}")
        logger.error(f"Setup failed: {str(e)}")

if __name__ == "__main__":
    main()
