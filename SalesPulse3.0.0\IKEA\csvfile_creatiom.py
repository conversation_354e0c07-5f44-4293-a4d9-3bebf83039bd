import pandas as pd
import os

def process_excel_to_csv(excel_file_path, sheet_names, output_csv_path):
    """
    Load Excel file with multiple sheets, remove missing values, and save as CSV
    
    Args:
        excel_file_path (str): Path to the Excel file
        sheet_names (list): List of sheet names to process
        output_csv_path (str): Path for the output CSV file
    """
    try:
        # Initialize empty list to store DataFrames
        dataframes = []
        
        # Load each sheet
        for sheet_name in sheet_names:
            print(f"Loading sheet: {sheet_name}")
            df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
            
            dataframes.append(df)
            print(f"Loaded {len(df)} rows from sheet: {sheet_name}")
        
        # Combine all sheets
        combined_df = pd.concat(dataframes, ignore_index=True)
        print(f"Combined DataFrame shape: {combined_df.shape}")
        
        # Check for missing values
        missing_values_before = combined_df.isnull().sum().sum()
        print(f"Total missing values before cleaning: {missing_values_before}")
        
        # Remove rows with missing values
        cleaned_df = combined_df.dropna()
        
        # Check missing values after cleaning
        missing_values_after = cleaned_df.isnull().sum().sum()
        print(f"Total missing values after cleaning: {missing_values_after}")
        print(f"Cleaned DataFrame shape: {cleaned_df.shape}")
        
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_csv_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Save to CSV
        cleaned_df.to_csv(output_csv_path, index=False)
        print(f"CSV file saved successfully at: {output_csv_path}")
        
        return cleaned_df
        
    except FileNotFoundError:
        print(f"Error: Excel file not found at {excel_file_path}")
        return None
    except ValueError as e:
        print(f"Error: Sheet name not found - {e}")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None

# Main execution
if __name__ == "__main__":
    # File paths
    excel_file_path = r'c:\Users\<USER>\SalesPulse\SalesPulse3.0.0\IKEA\Data.xlsx'
    output_csv_path = r'c:\Users\<USER>\SalesPulse\SalesPulse3.0.0\IKEA\cleaned_data.csv'
    
    # Sheet names to process
    sheet_names = ['DJA', 'YAS']
    
    # Process the Excel file
    result_df = process_excel_to_csv(excel_file_path, sheet_names, output_csv_path)
    
    # Display summary information
    if result_df is not None:
        print("\n" + "="*50)
        print("DATA PROCESSING SUMMARY")
        print("="*50)
        print(f"Total rows in final dataset: {len(result_df)}")
        print(f"Total columns: {len(result_df.columns)}")
        print(f"Columns: {list(result_df.columns)}")
        
        # Display first few rows
        print("\nFirst 5 rows:")
        print(result_df.head())
        
        # Display data types
        print("\nData types:")
        print(result_df.dtypes)
