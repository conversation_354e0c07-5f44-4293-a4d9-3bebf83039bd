# Core Azure packages - Required for the application
azure-core==1.35.0
azure-identity==1.23.1
azure-search-documents==11.5.3
azure-storage-blob==12.26.0

# AI/ML packages - Core functionality
langchain==0.2.6
langchain-community==0.2.6
langchain-core==0.2.10
langchain-openai==0.1.10
langchain-text-splitters==0.2.2
sentence-transformers==5.0.0
openai==1.97.0
faiss-cpu==1.8.0.post1
fastembed==0.2.7
huggingface-hub==0.33.4
tokenizers==0.21.2
safetensors==0.5.3
tiktoken==0.9.0

# Async/HTTP dependencies - Required for async operations
aiohttp==3.12.14
aiosignal==1.4.0
async-timeout==4.0.3
multidict==6.6.3
yarl==1.20.1
frozenlist==1.7.0
anyio==4.9.0
sniffio==1.3.1

# Data processing - Essential
numpy==1.26.4
pandas==2.3.1
matplotlib==3.9.0
seaborn==0.13.2
pillow==10.4.0
pyarrow==21.0.0
scipy==1.13.1

# Web framework and dependencies
streamlit==1.29.0
altair==5.5.0
blinker==1.9.0
validators==0.35.0
tornado==6.5.1
Flask==3.1.1
Werkzeug==3.1.3
itsdangerous==2.2.0

# Database
SQLAlchemy==2.0.41

# Core utilities and dependencies
attrs==25.3.0
tqdm==4.67.1
filelock==3.18.0
loguru==0.7.3
pydantic==2.11.5
pydantic_core==2.33.2
annotated-types==0.7.0
python-dotenv==1.1.0
PyYAML==6.0.2
reportlab==4.4.2
requests==2.32.4
beautifulsoup4==4.13.4
soupsieve==2.7

# JSON/Schema handling
jsonschema==4.24.1
jsonschema-specifications==2025.4.1
orjson==3.11.0
referencing==0.36.2
rpds-py==0.26.0

# Security and HTTP
certifi==2025.7.14
urllib3==2.5.0
httpx==0.28.1
httpcore==1.0.9
h11==0.16.0
cryptography==45.0.5
cffi==1.17.1
pycparser==2.22

# File processing
pypdf==5.8.0

# Essential utilities
click==8.1.8
Jinja2==3.1.6
MarkupSafe==3.0.2
packaging==23.2
python-dateutil==2.9.0.post0
six==1.17.0
typing_extensions==4.14.1
charset-normalizer==3.4.2
idna==3.10
tzdata==2025.2

# Additional required dependencies
cachetools==5.5.2
tenacity==8.5.0
joblib==1.5.1
threadpoolctl==3.6.0
fonttools==4.59.0
kiwisolver==1.4.7
pyparsing==3.2.3
cycler==0.12.1
contourpy==1.3.0
marshmallow==3.26.1
dataclasses-json==0.6.7
mypy_extensions==1.1.0
typing-inspect==0.9.0
langsmith==0.1.147
jiter==0.10.0
propcache==0.3.2
exceptiongroup==1.3.0
websocket-client==1.8.0
rich==13.9.4
markdown-it-py==3.0.0
mdurl==0.1.2
Pygments==2.19.2
pytz==2025.2
flatbuffers==25.2.10
regex==2024.11.6
requests-toolbelt==1.0.0
PyJWT==2.10.1
msal==1.32.3
msal-extensions==1.3.1
isodate==0.7.2