import sqlite3
import pandas as pd
import os

def create_ikea_database():
    """Create IKEA store database and load data from CSV"""
    
    # Database path
    db_path = "data/ikea_store_database.db"
    csv_path = "data/datasets/Store_data.csv"
    
    # Remove existing database if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed existing database: {db_path}")
    
    # Create connection
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create store_data table
    cursor.execute('''
        CREATE TABLE store_data (
            Region TEXT NOT NULL,
            Channel TEXT NOT NULL,
            Store TEXT NOT NULL,
            Date TEXT NOT NULL,
            Act REAL NOT NULL,
            Ly REAL NOT NULL,
            vs_Ly_percent REAL NOT NULL,
            Visitors INTEGER NOT NULL,
            Customers INTEGER NOT NULL,
            Conversion REAL NOT NULL,
            ATV REAL NOT NULL,
            Item_Sold INTEGER NOT NULL,
            Price_Item REAL NOT NULL,
            Item_Cust REAL NOT NULL,
            PRIMARY KEY (Region, Channel, Store, Date)
        )
    ''')
    
    print("Created store_data table")
    
    # Load CSV data
    if os.path.exists(csv_path):
        # Read CSV with original column names
        df = pd.read_csv(csv_path)
        print(f"Original columns: {list(df.columns)}")
        
        # Clean column names - remove spaces and handle special characters
        df.columns = df.columns.str.strip()  # Remove leading/trailing spaces
        
        # Map original column names to our database schema
        column_mapping = {
            'Region': 'Region',
            'Channel': 'Channel', 
            'Store': 'Store',
            'Date': 'Date',
            'Act': 'Act',
            'Ly': 'Ly',
            'vs Ly%': 'vs_Ly_percent',
            'Visitors': 'Visitors',
            'Customers': 'Customers', 
            'Conversion': 'Conversion',
            'ATV': 'ATV',
            'Item Sold': 'Item_Sold',
            'Price/Item': 'Price_Item',
            'Item/Cust': 'Item_Cust'
        }
        
        # Rename columns
        df = df.rename(columns=column_mapping)
        print(f"Renamed columns: {list(df.columns)}")
        
        # Clean the data
        # Strip whitespace from text columns
        for col in ['Region', 'Channel', 'Store']:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Ensure numeric columns are properly formatted
        numeric_columns = ['Act', 'Ly', 'vs_Ly_percent', 'Visitors', 'Customers', 
                          'Conversion', 'ATV', 'Item_Sold', 'Price_Item', 'Item_Cust']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Convert integer columns to int
        int_columns = ['Visitors', 'Customers', 'Item_Sold']
        for col in int_columns:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)
        
        # Insert data into database
        df.to_sql('store_data', conn, if_exists='append', index=False)
        print(f"Loaded {len(df)} records from {csv_path}")
    else:
        print(f"Warning: CSV file {csv_path} not found")
    
    # Verify data
    cursor.execute("SELECT COUNT(*) FROM store_data")
    count = cursor.fetchone()[0]
    print(f"Total records in database: {count}")
    
    # Show sample data
    cursor.execute("SELECT * FROM store_data LIMIT 5")
    sample_data = cursor.fetchall()
    print("\nSample data:")
    for row in sample_data:
        print(row)
    
    # Show data summary by store and channel
    cursor.execute("""
        SELECT Channel, Store, COUNT(*) as record_count, 
               AVG(Act) as avg_actuals, AVG(Visitors) as avg_visitors
        FROM store_data 
        GROUP BY Channel, Store
    """)
    summary = cursor.fetchall()
    print("\nData Summary by Store and Channel:")
    for row in summary:
        print(f"Channel: {row[0]}, Store: {row[1]}, Records: {row[2]}, Avg Actuals: {row[3]:.2f}, Avg Visitors: {row[4]:.0f}")
    
    # Commit and close
    conn.commit()
    conn.close()
    print(f"\nDatabase created successfully: {db_path}")

if __name__ == "__main__":
    create_ikea_database()
