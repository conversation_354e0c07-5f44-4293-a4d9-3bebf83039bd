"""
System Test Script for FinSight AI
Tests core functionality and system components
"""

import sys
import logging
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from config.config import get_system_config
from utils.llm_setup import test_llm_connection, get_llm
from utils.document_processor import DocumentProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_configuration():
    """Test system configuration"""
    print("🔧 Testing Configuration...")
    try:
        config = get_system_config()
        print(f"✅ Base directory: {config.base_dir}")
        print(f"✅ Companies configured: {len(config.companies)}")
        
        for company_key, company_config in config.companies.items():
            print(f"  - {company_config.display_name} ({company_config.color})")
        
        print(f"✅ Model config: {config.model_config.embedding_model}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def test_llm():
    """Test LLM connection"""
    print("\n🤖 Testing LLM Connection...")
    try:
        if test_llm_connection():
            print("✅ LLM connection successful")
            
            # Test basic functionality
            llm = get_llm()
            response = llm.invoke("What is 2+2?")
            print(f"✅ LLM response test: {response.content[:50]}...")
            return True
        else:
            print("❌ LLM connection failed")
            return False
    except Exception as e:
        print(f"❌ LLM test failed: {str(e)}")
        return False

def test_document_processor():
    """Test document processor"""
    print("\n📄 Testing Document Processor...")
    try:
        config = get_system_config()
        doc_processor = DocumentProcessor(config)
        
        # Test document stats
        stats = doc_processor.get_document_stats()
        print(f"✅ Document processor initialized")
        
        total_docs = sum(company_stats['pdf_count'] for company_stats in stats.values())
        print(f"📊 Total PDF documents found: {total_docs}")
        
        for company_key, company_stats in stats.items():
            company_name = company_stats['display_name']
            doc_count = company_stats['pdf_count']
            has_vectors = company_stats['has_vectors']
            
            print(f"  - {company_name}: {doc_count} PDFs, Vectors: {'✅' if has_vectors else '❌'}")
        
        return True
    except Exception as e:
        print(f"❌ Document processor test failed: {str(e)}")
        return False

def test_directories():
    """Test directory structure"""
    print("\n📁 Testing Directory Structure...")
    try:
        config = get_system_config()
        
        directories_to_check = [
            config.base_dir,
            config.documents_dir,
            config.vectors_dir,
            config.processed_dir,
            config.logs_dir
        ]
        
        for directory in directories_to_check:
            if directory.exists():
                print(f"✅ {directory.name}: {directory}")
            else:
                print(f"⚠️ {directory.name}: {directory} (will be created)")
        
        # Check company directories
        for company_key in config.companies.keys():
            company_dir = config.documents_dir / company_key
            if company_dir.exists():
                print(f"✅ {company_key} directory: {company_dir}")
            else:
                print(f"⚠️ {company_key} directory: {company_dir} (will be created)")
        
        return True
    except Exception as e:
        print(f"❌ Directory test failed: {str(e)}")
        return False

def test_imports():
    """Test all imports"""
    print("\n📦 Testing Imports...")
    try:
        # Test core imports
        from config.config import get_system_config
        from utils.document_processor import DocumentProcessor
        from utils.llm_setup import get_llm
        from utils.chatbot_engine import FinSightChatbot
        
        print("✅ All core modules imported successfully")
        
        # Test external dependencies
        import streamlit
        import langchain
        import faiss
        from langchain_community.document_loaders import PyPDFLoader
        from langchain_community.embeddings.fastembed import FastEmbedEmbeddings
        
        print("✅ All external dependencies available")
        return True
    except ImportError as e:
        print(f"❌ Import test failed: {str(e)}")
        print("💡 Try: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🎯 FinSight AI - System Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Directories", test_directories),
        ("Document Processor", test_document_processor),
        ("LLM Connection", test_llm),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! FinSight AI is ready to use.")
        print("\n🚀 Next steps:")
        print("1. Add PDF documents to the documents/ directories")
        print("2. Run: python setup_documents.py")
        print("3. Run: streamlit run app.py")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please address the issues above.")
        
        if not any(name == "LLM Connection" and result for name, result in results):
            print("\n💡 LLM Connection failed? Check your .env file:")
            print("   - Copy .env.example to .env")
            print("   - Add your Azure OpenAI or OpenAI API keys")

if __name__ == "__main__":
    main()
