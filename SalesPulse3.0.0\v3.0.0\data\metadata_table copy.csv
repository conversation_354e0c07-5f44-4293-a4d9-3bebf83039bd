Table Name,Column Name,Data Type,Primary Key,Foreign Key,Description
event_table,date,TEXT,Yes,No,Date of the event
event_table,event_type,TEXT,No,No,"Type of event (e.g., ""Promotion"", ""EID"", ""Storm"")"
store_opening_table,store_id,TEXT,Yes,No,"Unique identifier of each store (e.g., ""SRT-5545"")."
store_opening_table,store_opening_date,TEXT,No,No,Date when the store opened.
weather_table,date,TEXT,Yes,Yes (References event_table.date),Date of record.
weather_table,avg_temperature,REAL,No,No,Average temperature (°C).
weather_table,temperature_type,TEXT,No,No,"Categorical temperature label based on avg temperature (e.g. `Cool`, `Moderate`, `Hot`)."
weather_table,min_temperature,REAL,No,No, Minimum temperature (°C).
weather_table,max_temperature,REAL,No,No,Maximum temperature (°C).
weather_table,precipitation,REAL,No,No,Daily precipitation (mm).
weather_table,rainfall_type,TEXT,No,No,"Categorical Rainfall intensity label based on precipitation (e.g. `No Rain`, `Light Rain`, `Heavy Rain`)."
weather_table,city,TEXT,Yes,No,City of record.
forecast_data_table,store_id,TEXT,No,Yes (References store_opening_table.store_id),Identifier of stores.
forecast_data_table,pizza_size_id,TEXT,No,No,"Pizza type and size (e.g., ""five_cheese_l"",""bbq_ckn_l"")."
forecast_data_table,date,TEXT,No,Yes (References event_table.date),Date of the forecast/data.
forecast_data_table,quantity,INTEGER,No,No,Number of Pizzas ordered or predicted.
forecast_data_table,unit_cost_price,REAL,No,No,Cost price of a unit (INR).
forecast_data_table,unit_selling_price,REAL,No,No,Selling price of a unit (INR).
forecast_data_table,is_forecast,INTEGER,No,No,"1 if data is forecasted, 0 if actual."
forecast_data_table,forecast_revenue_INR,REAL,No,No,Forecasted revenue (INR).
forecast_data_table,pizza_name,TEXT,No,No,Name of the pizza.
pizza_data_table,order_id,TEXT,No,No,"Order identifier (e.g., ""Ord-345"")."
pizza_data_table,store_id,TEXT,No,Yes (References store_opening_table.store_id),"Identifier of the store where the order was placed (e.g., ""SRT-3655"")."
pizza_data_table,store_format,TEXT,No,"Yes (References weather_table.city ,date)","Format/type of store (`Regular`, `Highway`, `Mall`)."
pizza_data_table,city,TEXT,No,No,"City where the store is located (e.g., ""Mumbai"")."
pizza_data_table,region,TEXT,No,No,"Region classification (`West`, `North`, `South`, `East`)."
pizza_data_table,country,TEXT,No,No,Country name (`India`).
pizza_data_table,pizza_size_id,TEXT,No,No,"ID for pizza with size (e.g., ""hawaiian_m"",""bbq_ckn_s"")."
pizza_data_table,quantity,TEXT,No,No,Number of pizzas ordered.
pizza_data_table,date_time,TEXT,No,"Yes (References weather_table.city ,date)",Timestamp when the order was placed.
pizza_data_table,month_name,TEXT,No,No,"Month name when the order was placed (e.g., ""January"")."
pizza_data_table,quarter_of_year,TEXT,No,No,"Quarter of the year when the order was placed(`Quarter1`, etc.)."
pizza_data_table,year,TEXT,No,No,Year of the order when the order was placed.
pizza_data_table,day_name,TEXT,No,No,"Day name  when the order was placed(e.g., ""Monday"")."
pizza_data_table,hourly_interval,TEXT,No,No,"Time window in hour when the order was placed (e.g., ""11-12 noon"",""01-02 pm"")."
pizza_data_table,week_of_year,TEXT,No,No,"Week number of the year when the order was placed (e.g., ""2"",""30"")."
pizza_data_table,pizza_type_id,TEXT,No,No,"Type of pizza ordered (e.g., ""five_cheese"".""bbq_ckn"")."
pizza_data_table,pizza_size,TEXT,No,No,"Size of pizza ordered (`M`, `L`, `S`, `XL`, `XXL`)."
pizza_data_table,unit_cost_price,TEXT,No,No,Cost of one pizza to store (INR).
pizza_data_table,unit_selling_price,TEXT,No,No,Selling price of one pizza (INR).
pizza_data_table,pizza_name,TEXT,No,No,"Full pizza name (e.g., ""Classic Deluxe Pizza"",""Barbecue Chicken Pizza"")."
pizza_data_table,pizza_category,TEXT,No,No,"Category of pizza (`Classic`, `Veggie`, `Supreme`, `Chicken`)."
pizza_data_table,revenue,TEXT,No,No,Total revenue from order (INR).
pizza_data_table,total_cost,TEXT,No,No,Total cost for the order (INR).
pizza_data_table,profit,TEXT,No,No,Profit made from the order (INR).
pizza_data_table,customer_name,TEXT,No,No,Name of customer who placed the order.
pizza_data_table,recommended_pizza_name,TEXT,No,No,Suggested pizza for customer.
pizza_data_table,reason_for_recomendation,TEXT,No,No,Explanation for recommending pizzas to the customer.