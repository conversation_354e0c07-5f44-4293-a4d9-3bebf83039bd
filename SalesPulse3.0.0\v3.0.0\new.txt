# Core Streamlit application
streamlit==1.29.0

# Azure integration
azure-storage-blob==12.26.0
azure-identity==1.23.1
azure-core==1.35.0

# LangChain ecosystem (minimal for your agent)
langchain==0.2.6
langchain-community==0.2.6
langchain-core==0.2.10
langchain-experimental==0.0.62
langchain-openai==0.1.10

# Data processing
pandas==2.3.1
numpy==1.26.4

# Visualization
matplotlib==3.9.0
pillow==10.4.0

# Configuration and utilities
python-dotenv==1.1.0
PyYAML==6.0.2
pydantic==2.11.5

# Database
SQLAlchemy==2.0.41

# Logging
loguru==0.7.3

# Core web libraries
requests==2.32.4
urllib3==2.5.0
certifi==2025.7.14

# Essential dependencies for LangChain
tiktoken==0.9.0
typing_extensions==4.14.1
