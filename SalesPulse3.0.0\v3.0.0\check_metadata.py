#!/usr/bin/env python3
"""
Check the structure of the metadata DataFrame
"""
import pandas as pd
import pickle

def check_metadata_structure():
    """Check what columns are in the ikea_metadata.pkl file"""
    try:
        # Check ikea_metadata.pkl
        print("🔍 Checking ikea_metadata.pkl structure...")
        with open('data/ikea_metadata.pkl', 'rb') as f:
            ikea_df = pickle.load(f)
        
        print(f"IKEA DataFrame shape: {ikea_df.shape}")
        print(f"IKEA DataFrame columns: {list(ikea_df.columns)}")
        print(f"IKEA DataFrame head:\n{ikea_df.head()}")
        
    except FileNotFoundError:
        print("❌ ikea_metadata.pkl not found")
    
    try:
        # Check regular metadata.pkl
        print("\n🔍 Checking metadata.pkl structure...")
        with open('data/metadata.pkl', 'rb') as f:
            regular_df = pickle.load(f)
        
        print(f"Regular DataFrame shape: {regular_df.shape}")
        print(f"Regular DataFrame columns: {list(regular_df.columns)}")
        print(f"Regular DataFrame head:\n{regular_df.head()}")
        
    except FileNotFoundError:
        print("❌ metadata.pkl not found")

if __name__ == "__main__":
    check_metadata_structure()
