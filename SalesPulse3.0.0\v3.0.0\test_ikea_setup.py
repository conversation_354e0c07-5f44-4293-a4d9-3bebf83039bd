"""
Test script to verify IKEA Store Analytics setup
"""
import os
import sqlite3
import pandas as pd

def test_ikea_setup():
    """Test all IKEA components"""
    
    print("🏪 Testing IKEA Store Analytics Setup\n" + "="*50)
    
    # Test 1: Database connection
    print("1. Testing Database Connection...")
    db_path = "data/ikea_store_database.db"
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='store_data'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   ✅ Database exists and store_data table found")
            
            # Get record count
            cursor.execute("SELECT COUNT(*) FROM store_data")
            count = cursor.fetchone()[0]
            print(f"   ✅ Total records: {count}")
            
            # Get sample data
            cursor.execute("SELECT DISTINCT Store, Channel FROM store_data")
            stores = cursor.fetchall()
            print(f"   ✅ Store-Channel combinations: {stores}")
            
        else:
            print("   ❌ store_data table not found")
            
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Test 2: Metadata file
    print("\n2. Testing Metadata File...")
    metadata_path = "data/ikea_metadata_table.csv"
    try:
        if os.path.exists(metadata_path):
            df = pd.read_csv(metadata_path)
            print(f"   ✅ Metadata file exists with {len(df)} columns defined")
            print(f"   ✅ Columns: {list(df['Column Name'].unique())}")
        else:
            print("   ❌ Metadata file not found")
    except Exception as e:
        print(f"   ❌ Metadata error: {e}")
    
    # Test 3: Vector files
    print("\n3. Testing Vector Files...")
    vector_files = [
        ("data/ikea_sql_vectors.faiss", "FAISS Index"),
        ("data/ikea_metadata.pkl", "Metadata Pickle")
    ]
    
    for file_path, file_type in vector_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_type} exists: {file_path}")
        else:
            print(f"   ⚠️  {file_type} not found: {file_path} (will be created on first run)")
    
    # Test 4: Configuration files
    print("\n4. Testing Configuration Files...")
    config_files = [
        ("configuration/ikea_store_prompts.yaml", "IKEA Prompts"),
        ("constants/constant.py", "Constants"),
        ("tools/tools.py", "Tools"),
        ("tools/fallback_tools.py", "Fallback Tools")
    ]
    
    for file_path, file_type in config_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_type} exists: {file_path}")
        else:
            print(f"   ❌ {file_type} not found: {file_path}")
    
    # Test 5: Sample SQL query
    print("\n5. Testing Sample SQL Query...")
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test query: Total actuals by store
        cursor.execute("""
            SELECT Store, SUM(Act) as total_actuals, COUNT(*) as record_count 
            FROM store_data 
            GROUP BY Store
        """)
        results = cursor.fetchall()
        
        print("   ✅ Sample query results:")
        for store, actuals, count in results:
            print(f"      {store}: {actuals:,.2f} AED ({count} records)")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ SQL query error: {e}")
    
    print("\n" + "="*50)
    print("🎯 IKEA Store Analytics Setup Test Complete!")
    print("\nTo run the application:")
    print("   streamlit run assistant.py")
    print("\nSample questions to ask:")
    print("   • What are the total actuals for DJA store?")
    print("   • Compare conversion rates between Store and IFB channels")
    print("   • Show me the daily performance trends")
    print("   • Which store has better ATV performance?")

if __name__ == "__main__":
    test_ikea_setup()
