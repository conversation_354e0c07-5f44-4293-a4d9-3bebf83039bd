#!/usr/bin/env python3
"""
Quick Analysis Script for DJA Weekend vs Weekday Performance
This bypasses all the complex agent issues and gives immediate results
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os

# Connect to the IKEA database
def analyze_dja_weekend_performance():
    """Analyze DJA store weekend vs weekday performance"""
    
    # Connect to database
    db_path = 'data/ikea_store_database.db'
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    
    # Main query for DJA weekend vs weekday analysis
    query = """
    SELECT 
        CASE 
            WHEN strftime('%w', Date) IN ('0', '6') THEN 'Weekend'
            ELSE 'Weekday'
        END AS Day_Type,
        COUNT(*) AS Number_of_Days,
        SUM(Act) AS Total_Revenue_AED,
        SUM(Visitors) AS Total_Visitors,
        SUM(Customers) AS Total_Customers,
        ROUND((SUM(Customers) * 100.0 / SUM(Visitors)), 2) AS Conversion_Rate_Percent,
        ROUND(AVG(ATV), 2) AS Average_ATV_AED,
        SUM(Item_Sold) AS Total_Items_Sold,
        ROUND(SUM(Act) / COUNT(*), 2) AS Daily_Avg_Revenue_AED,
        ROUND(SUM(Visitors) / COUNT(*), 2) AS Daily_Avg_Visitors,
        ROUND(SUM(Customers) / COUNT(*), 2) AS Daily_Avg_Customers
    FROM store_data 
    WHERE Store = 'DJA' 
    AND Date BETWEEN '2025-01-01' AND '2025-06-30'
    GROUP BY Day_Type
    ORDER BY Day_Type DESC
    """
    
    try:
        # Execute query and get results
        df = pd.read_sql_query(query, conn)
        
        print("=" * 80)
        print("🏪 DJA STORE WEEKEND vs WEEKDAY PERFORMANCE ANALYSIS")
        print("📅 Period: January - June 2025")
        print("=" * 80)
        
        if df.empty:
            print("❌ No data found for DJA store in the specified period")
            return
        
        # Display results table
        print("\n📊 PERFORMANCE SUMMARY:")
        print("-" * 80)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        print(df.to_string(index=False))
        
        # Calculate insights
        weekend_data = df[df['Day_Type'] == 'Weekend'].iloc[0] if not df[df['Day_Type'] == 'Weekend'].empty else None
        weekday_data = df[df['Day_Type'] == 'Weekday'].iloc[0] if not df[df['Day_Type'] == 'Weekday'].empty else None
        
        if weekend_data is not None and weekday_data is not None:
            print("\n" + "=" * 80)
            print("🔍 KEY INSIGHTS & COMPARISONS:")
            print("=" * 80)
            
            # Revenue comparison
            revenue_diff = ((weekend_data['Total_Revenue_AED'] - weekday_data['Total_Revenue_AED']) / weekday_data['Total_Revenue_AED']) * 100
            print(f"💰 Revenue Performance:")
            print(f"   • Weekend Total: AED {weekend_data['Total_Revenue_AED']:,.2f}")
            print(f"   • Weekday Total: AED {weekday_data['Total_Revenue_AED']:,.2f}")
            print(f"   • Difference: {revenue_diff:+.1f}% ({'Weekend performs better' if revenue_diff > 0 else 'Weekday performs better'})")
            
            # Daily average revenue
            daily_revenue_diff = ((weekend_data['Daily_Avg_Revenue_AED'] - weekday_data['Daily_Avg_Revenue_AED']) / weekday_data['Daily_Avg_Revenue_AED']) * 100
            print(f"   • Weekend Daily Avg: AED {weekend_data['Daily_Avg_Revenue_AED']:,.2f}")
            print(f"   • Weekday Daily Avg: AED {weekday_data['Daily_Avg_Revenue_AED']:,.2f}")
            print(f"   • Daily Avg Difference: {daily_revenue_diff:+.1f}%")
            
            # Visitor comparison
            visitor_diff = ((weekend_data['Daily_Avg_Visitors'] - weekday_data['Daily_Avg_Visitors']) / weekday_data['Daily_Avg_Visitors']) * 100
            print(f"\n👥 Visitor Performance:")
            print(f"   • Weekend Daily Avg Visitors: {weekend_data['Daily_Avg_Visitors']:,.0f}")
            print(f"   • Weekday Daily Avg Visitors: {weekday_data['Daily_Avg_Visitors']:,.0f}")
            print(f"   • Difference: {visitor_diff:+.1f}% ({'Weekend has more visitors' if visitor_diff > 0 else 'Weekday has more visitors'})")
            
            # Conversion rate comparison
            conv_diff = weekend_data['Conversion_Rate_Percent'] - weekday_data['Conversion_Rate_Percent']
            print(f"\n📈 Conversion Rate Performance:")
            print(f"   • Weekend Conversion Rate: {weekend_data['Conversion_Rate_Percent']:.2f}%")
            print(f"   • Weekday Conversion Rate: {weekday_data['Conversion_Rate_Percent']:.2f}%")
            print(f"   • Difference: {conv_diff:+.2f} percentage points")
            
            # ATV comparison
            atv_diff = ((weekend_data['Average_ATV_AED'] - weekday_data['Average_ATV_AED']) / weekday_data['Average_ATV_AED']) * 100
            print(f"\n💳 Average Transaction Value (ATV):")
            print(f"   • Weekend ATV: AED {weekend_data['Average_ATV_AED']:,.2f}")
            print(f"   • Weekday ATV: AED {weekday_data['Average_ATV_AED']:,.2f}")
            print(f"   • Difference: {atv_diff:+.1f}%")
            
            print("\n" + "=" * 80)
            print("📋 EXECUTIVE SUMMARY:")
            print("=" * 80)
            
            if daily_revenue_diff > 0:
                print(f"✅ Weekend days generate {abs(daily_revenue_diff):.1f}% more revenue per day than weekdays")
            else:
                print(f"⚠️ Weekday days generate {abs(daily_revenue_diff):.1f}% more revenue per day than weekends")
            
            if visitor_diff > 0:
                print(f"✅ Weekend days attract {abs(visitor_diff):.1f}% more visitors per day than weekdays")
            else:
                print(f"⚠️ Weekday days attract {abs(visitor_diff):.1f}% more visitors per day than weekends")
                
            if conv_diff > 0:
                print(f"✅ Weekend conversion rate is {abs(conv_diff):.2f} percentage points higher")
            else:
                print(f"⚠️ Weekday conversion rate is {abs(conv_diff):.2f} percentage points higher")
                
            # Overall performance assessment
            better_performer = "Weekend" if daily_revenue_diff > 0 else "Weekday"
            print(f"\n🎯 OVERALL WINNER: {better_performer} performance is stronger for DJA store")
        
        # Detailed monthly breakdown query
        monthly_query = """
        SELECT 
            strftime('%Y-%m', Date) AS Month,
            CASE 
                WHEN strftime('%w', Date) IN ('0', '6') THEN 'Weekend'
                ELSE 'Weekday'
            END AS Day_Type,
            SUM(Act) AS Revenue_AED,
            AVG(Conversion) AS Avg_Conversion_Rate,
            AVG(ATV) AS Avg_ATV
        FROM store_data 
        WHERE Store = 'DJA' 
        AND Date BETWEEN '2025-01-01' AND '2025-06-30'
        GROUP BY Month, Day_Type
        ORDER BY Month, Day_Type DESC
        """
        
        monthly_df = pd.read_sql_query(monthly_query, conn)
        
        if not monthly_df.empty:
            print(f"\n📅 MONTHLY BREAKDOWN:")
            print("-" * 80)
            print(monthly_df.to_string(index=False))
        
        conn.close()
        print(f"\n✅ Analysis Complete! Database connection closed.")
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        conn.close()

if __name__ == "__main__":
    analyze_dja_weekend_performance()
