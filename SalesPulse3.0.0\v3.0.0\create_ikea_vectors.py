"""
IKEA Store Analytics SQL Examples for Vector Database
"""
import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss
import pickle
import os

# Sample SQL queries and answers for IKEA store data
sql_examples = [
    {
        "question": "What are the total actuals for DJA store in January 2025?",
        "sql": "SELECT SUM(Act) as total_actuals FROM store_data WHERE Store = 'DJA' AND Date LIKE '2025-01%'",
        "context": "Calculating total revenue for DJA store in January 2025"
    },
    {
        "question": "What is the conversion rate for YAS store by channel?",
        "sql": "SELECT Channel, AVG(Conversion) as avg_conversion FROM store_data WHERE Store = 'YAS' GROUP BY Channel",
        "context": "Analyzing customer conversion rates by channel for YAS store"
    },
    {
        "question": "Compare store performance between DJA and YAS",
        "sql": "SELECT Store, AVG(Act) as avg_actuals, AVG(Visitors) as avg_visitors, AVG(ATV) as avg_atv FROM store_data GROUP BY Store",
        "context": "Comparing key performance metrics between DJA and YAS stores"
    },
    {
        "question": "What is the year-over-year growth for IKEA Food & Beverages?",
        "sql": "SELECT AVG(vs_Ly_percent) as avg_growth FROM store_data WHERE Channel = 'IFB'",
        "context": "Analyzing year-over-year growth for IKEA Food & Beverages channel"
    },
    {
        "question": "Show daily performance trends for January 2025",
        "sql": "SELECT Date, SUM(Act) as daily_actuals, SUM(Visitors) as daily_visitors FROM store_data WHERE Date LIKE '2025-01%' GROUP BY Date ORDER BY Date",
        "context": "Daily performance trends across all stores and channels"
    },
    {
        "question": "What are the top performing days by revenue?",
        "sql": "SELECT Date, SUM(Act) as total_revenue FROM store_data GROUP BY Date ORDER BY total_revenue DESC LIMIT 10",
        "context": "Identifying top revenue generating days"
    },
    {
        "question": "Calculate average transaction value by store and channel",
        "sql": "SELECT Store, Channel, AVG(ATV) as avg_transaction_value FROM store_data GROUP BY Store, Channel",
        "context": "Average transaction value analysis by store and channel"
    },
    {
        "question": "Find days with highest customer conversion",
        "sql": "SELECT Date, Store, Channel, Conversion FROM store_data ORDER BY Conversion DESC LIMIT 10",
        "context": "Identifying best customer conversion performance"
    },
    {
        "question": "Compare item sales between Store and IFB channels",
        "sql": "SELECT Channel, AVG(Item_Sold) as avg_items, AVG(Price_Item) as avg_price FROM store_data GROUP BY Channel",
        "context": "Comparing item sales and pricing between Store and IFB channels"
    },
    {
        "question": "Show weekly aggregated performance",
        "sql": "SELECT strftime('%Y-%W', Date) as week, SUM(Act) as weekly_actuals, SUM(Visitors) as weekly_visitors, AVG(Conversion) as avg_conversion FROM store_data GROUP BY strftime('%Y-%W', Date) ORDER BY week",
        "context": "Weekly performance aggregation across all metrics"
    },
    {
        "question": "Find underperforming days compared to last year",
        "sql": "SELECT Date, Store, Channel, Act, Ly, vs_Ly_percent FROM store_data WHERE vs_Ly_percent < 0 ORDER BY vs_Ly_percent",
        "context": "Identifying days with negative year-over-year performance"
    },
    {
        "question": "Calculate items per customer by store",
        "sql": "SELECT Store, AVG(Item_Cust) as avg_items_per_customer FROM store_data GROUP BY Store",
        "context": "Average items purchased per customer by store"
    },
    {
        "question": "Show price per item trends",
        "sql": "SELECT Date, Store, Channel, Price_Item FROM store_data ORDER BY Date, Store, Channel",
        "context": "Price per item trends over time by store and channel"
    },
    {
        "question": "Find best performing store-channel combinations",
        "sql": "SELECT Store, Channel, AVG(Act) as avg_revenue, AVG(Conversion) as avg_conversion, AVG(ATV) as avg_atv FROM store_data GROUP BY Store, Channel ORDER BY avg_revenue DESC",
        "context": "Best performing store and channel combinations by multiple metrics"
    },
    {
        "question": "Calculate total visitors and customers for the period",
        "sql": "SELECT SUM(Visitors) as total_visitors, SUM(Customers) as total_customers, ROUND(CAST(SUM(Customers) AS FLOAT) / SUM(Visitors) * 100, 2) as overall_conversion FROM store_data",
        "context": "Overall visitor, customer, and conversion statistics"
    }
]

def create_ikea_vectors():
    """Create FAISS vector database for IKEA SQL examples"""
    
    print("Creating IKEA SQL vector database...")
    
    # Initialize sentence transformer
    model = SentenceTransformer("all-MiniLM-L6-v2", token=False)
    
    # Prepare data for vectorization
    questions = [example["question"] for example in sql_examples]
    
    # Create embeddings
    print("Creating embeddings...")
    embeddings = model.encode(questions)
    
    # Create FAISS index
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    index.add(embeddings.astype('float32'))
    
    # Create dataframe with examples
    df = pd.DataFrame(sql_examples)
    
    # Save FAISS index
    faiss_path = "data/ikea_sql_vectors.faiss"
    faiss.write_index(index, faiss_path)
    print(f"FAISS index saved to: {faiss_path}")
    
    # Save dataframe
    df_path = "data/ikea_metadata.pkl"
    df.to_pickle(df_path)
    print(f"Metadata saved to: {df_path}")
    
    print(f"Vector database created with {len(sql_examples)} examples")
    
    # Test the vector search
    print("\nTesting vector search...")
    test_query = "show me revenue for DJA store"
    test_embedding = model.encode([test_query])
    distances, indices = index.search(test_embedding.astype('float32'), k=3)
    
    print(f"Test query: '{test_query}'")
    print("Top 3 matches:")
    for i, idx in enumerate(indices[0]):
        print(f"  {i+1}. {df.iloc[idx]['question']} (distance: {distances[0][i]:.4f})")
        print(f"     SQL: {df.iloc[idx]['sql']}")
    
    return index, df

if __name__ == "__main__":
    print(f"Generated {len(sql_examples)} SQL examples for IKEA store analytics")
    
    # Create the vector database
    try:
        index, df = create_ikea_vectors()
        print("✅ IKEA vector database created successfully!")
    except Exception as e:
        print(f"❌ Error creating vector database: {e}")
        print("Vector database creation failed, but SQL examples are available")
