"""
Create IKEA Document Vector Database for RAG System
"""
import os
import shutil
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.embeddings.fastembed import FastEmbedEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter

def create_ikea_document_vectors():
    """Create vector database from IKEA PDF documents"""
    
    print("Creating IKEA document vector database...")
    
    # Paths
    documents_dir = "data/documents"
    vector_db_path = "data/retriever"
    
    # Check if documents exist
    if not os.path.exists(documents_dir):
        print(f"❌ Documents directory not found: {documents_dir}")
        return
    
    # Get PDF files
    pdf_files = [f for f in os.listdir(documents_dir) if f.endswith('.pdf') and f.startswith('IKEA')]
    
    if not pdf_files:
        print("❌ No IKEA PDF documents found")
        return
    
    print(f"Found {len(pdf_files)} IKEA documents: {pdf_files}")
    
    # Load documents
    documents = []
    for pdf_file in pdf_files:
        pdf_path = os.path.join(documents_dir, pdf_file)
        try:
            loader = PyPDFLoader(pdf_path)
            docs = loader.load()
            print(f"✅ Loaded {len(docs)} pages from {pdf_file}")
            documents.extend(docs)
        except Exception as e:
            print(f"❌ Error loading {pdf_file}: {e}")
    
    if not documents:
        print("❌ No documents were loaded successfully")
        return
    
    print(f"Total pages loaded: {len(documents)}")
    
    # Split documents into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
        length_function=len,
    )
    
    splits = text_splitter.split_documents(documents)
    print(f"Created {len(splits)} text chunks")
    
    # Create embeddings
    try:
        embeddings = FastEmbedEmbeddings(model_name="BAAI/bge-base-en-v1.5")
        print("✅ Embeddings model loaded")
    except Exception as e:
        print(f"❌ Error loading embeddings: {e}")
        return
    
    # Create vector database
    try:
        vector_db = FAISS.from_documents(splits, embeddings)
        print("✅ Vector database created")
    except Exception as e:
        print(f"❌ Error creating vector database: {e}")
        return
    
    # Save vector database
    try:
        # Backup existing vector db if it exists
        if os.path.exists(vector_db_path):
            backup_path = vector_db_path + "_backup"
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            shutil.move(vector_db_path, backup_path)
            print(f"✅ Backed up existing vector database to {backup_path}")
        
        # Save new vector database
        vector_db.save_local(vector_db_path)
        print(f"✅ Vector database saved to {vector_db_path}")
        
        # Test retrieval
        test_query = "What services does IKEA store provide?"
        retriever = vector_db.as_retriever(search_kwargs={"k": 3})
        results = retriever.get_relevant_documents(test_query)
        
        print(f"\nTest query: '{test_query}'")
        print(f"Retrieved {len(results)} relevant documents:")
        for i, doc in enumerate(results, 1):
            print(f"  {i}. {doc.page_content[:100]}...")
            
    except Exception as e:
        print(f"❌ Error saving vector database: {e}")
        return
    
    print("\n✅ IKEA document vector database created successfully!")

if __name__ == "__main__":
    try:
        create_ikea_document_vectors()
    except ImportError as e:
        print(f"❌ Missing required library: {e}")
        print("Please install required packages:")
        print("pip install langchain langchain-community faiss-cpu fastembed pypdf")
    except Exception as e:
        print(f"❌ Error: {e}")
