"""
LLM Setup Module for FinSight AI
Handles LLM initialization and configuration
"""

import os
import logging
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from langchain_openai import AzureChatOpenAI, ChatOpenAI
from langchain.globals import set_llm_cache
from langchain_community.cache import SQLiteCache

from config.config import get_system_config, AZURE_CONFIG

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class LLMManager:
    """Manages LLM initialization and configuration"""
    
    def __init__(self):
        self.config = get_system_config()
        self.llm = None
        self._setup_cache()
    
    def _setup_cache(self):
        """Setup LLM caching"""
        cache_path = self.config.base_dir / "data" / "llm_cache.sqlite"
        set_llm_cache(SQLiteCache(database_path=str(cache_path)))
        logger.info(f"LLM cache setup at: {cache_path}")
    
    def get_azure_llm(self) -> Optional[AzureChatOpenAI]:
        """Initialize Azure OpenAI LLM"""
        try:
            # Check if all required Azure config is available
            required_keys = ["api_key", "endpoint", "deployment_name"]
            missing_keys = [key for key in required_keys if not AZURE_CONFIG.get(key)]
            
            if missing_keys:
                logger.warning(f"Missing Azure config keys: {missing_keys}")
                return None
            
            llm = AzureChatOpenAI(
                openai_api_key=AZURE_CONFIG["api_key"],
                azure_endpoint=AZURE_CONFIG["endpoint"],
                openai_api_version=AZURE_CONFIG["api_version"],
                deployment_name=AZURE_CONFIG["deployment_name"],
                temperature=self.config.model_config.temperature,
                max_tokens=self.config.model_config.max_tokens,
                timeout=180,
                max_retries=3,
                cache=True
            )
            
            logger.info("Azure OpenAI LLM initialized successfully")
            return llm
            
        except Exception as e:
            logger.error(f"Error initializing Azure OpenAI LLM: {str(e)}")
            return None
    
    def get_openai_llm(self) -> Optional[ChatOpenAI]:
        """Initialize OpenAI LLM"""
        try:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                logger.warning("OPENAI_API_KEY not found in environment variables")
                return None
            
            llm = ChatOpenAI(
                openai_api_key=api_key,
                model_name=os.getenv("OPENAI_MODEL", "gpt-4"),
                temperature=self.config.model_config.temperature,
                max_tokens=self.config.model_config.max_tokens,
                timeout=180,
                max_retries=3,
                cache=True
            )
            
            logger.info("OpenAI LLM initialized successfully")
            return llm
            
        except Exception as e:
            logger.error(f"Error initializing OpenAI LLM: {str(e)}")
            return None
    
    def get_llm(self, prefer_azure: bool = True):
        """Get the best available LLM"""
        if self.llm:
            return self.llm
        
        if prefer_azure:
            # Try Azure first
            self.llm = self.get_azure_llm()
            if self.llm:
                return self.llm
            
            # Fallback to OpenAI
            logger.info("Azure LLM not available, trying OpenAI...")
            self.llm = self.get_openai_llm()
        else:
            # Try OpenAI first
            self.llm = self.get_openai_llm()
            if self.llm:
                return self.llm
            
            # Fallback to Azure
            logger.info("OpenAI LLM not available, trying Azure...")
            self.llm = self.get_azure_llm()
        
        if not self.llm:
            raise RuntimeError("No LLM could be initialized. Please check your API keys and configuration.")
        
        return self.llm
    
    def test_llm(self) -> bool:
        """Test if the LLM is working properly"""
        try:
            llm = self.get_llm()
            response = llm.invoke("Hello, this is a test message. Please respond with 'Test successful'.")
            
            if "test successful" in response.content.lower():
                logger.info("LLM test successful")
                return True
            else:
                logger.warning(f"LLM test returned unexpected response: {response.content}")
                return False
                
        except Exception as e:
            logger.error(f"LLM test failed: {str(e)}")
            return False

# Global LLM manager instance
llm_manager = LLMManager()

def get_llm():
    """Get the configured LLM instance"""
    return llm_manager.get_llm()

def test_llm_connection():
    """Test LLM connection"""
    return llm_manager.test_llm()

# Environment setup helper
def setup_environment():
    """Setup environment variables and configurations"""
    # Disable SSL warnings for development
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    
    # Setup LangChain tracing if API key is available
    langchain_api_key = os.getenv("LANGCHAIN_API_KEY")
    if langchain_api_key:
        os.environ['LANGCHAIN_TRACING_V2'] = 'true'
        os.environ['LANGCHAIN_API_KEY'] = langchain_api_key
        logger.info("LangChain tracing enabled")
    else:
        logger.info("LangChain tracing disabled (no API key)")

# Call setup on import
setup_environment()
