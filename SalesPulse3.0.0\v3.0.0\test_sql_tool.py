#!/usr/bin/env python3
"""
Test the SQL tool functionality directly
"""
import sys
import os
sys.path.append('.')

from utils.tools import create_retriver_for_sql
from data.dataloader import *

def test_sql_tool():
    """Test the SQL tool with a simple query"""
    print("🧪 Testing SQL Tool Functionality...")
    
    try:
        # Test query
        test_query = "How does weekend performance compare to weekday performance for DJA?"
        print(f"📝 Test Query: {test_query}")
        
        # Call the SQL tool
        result = create_retriver_for_sql(test_query)
        print(f"✅ SQL Tool Result:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Error testing SQL tool: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sql_tool()
