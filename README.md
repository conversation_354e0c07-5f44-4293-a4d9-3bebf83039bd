# SalesPulse v3.0.0 🍕

SalesPulse is an AI-powered business intelligence assistant specifically designed for pizza chain analytics. It provides intelligent insights, data visualization, and business analysis capabilities through a conversational interface powered by advanced language models and retrieval-augmented generation (RAG).

## 🎯 Overview

SalesPulse transforms complex pizza business data into actionable insights through natural language queries. The system combines SQL database querying, document retrieval, and AI-powered analysis to deliver comprehensive business intelligence for pizza chain operations.

### Key Features

- **Natural Language to SQL**: Convert business questions into SQL queries automatically
- **Interactive Chat Interface**: Streamlit-based conversational UI with authentication
- **Multi-Modal Analysis**: Support for text, charts, and document-based insights
- **Fallback Agent System**: Robust error handling with intelligent fallback mechanisms
- **Vector Database Integration**: FAISS-powered semantic search for documents and metadata
- **Real-time Visualization**: Dynamic charts and graphs for data presentation
- **Comprehensive Logging**: Detailed logging system for monitoring and debugging

## 🏗️ Architecture

```text
SalesPulse/
├── v3.0.0/                    # Main application directory
│   ├── assistant.py           # Streamlit web interface
│   ├── main.py               # Core processing logic
│   ├── logger.py             # Logging configuration
│   ├── requirements.txt      # Python dependencies
│   ├── workbook.ipynb        # Jupyter notebook for development
│   │
│   ├── configuration/        # Configuration management
│   │   ├── config.py         # Session configuration
│   │   └── prompts.yaml      # AI prompt templates
│   │
│   ├── constants/            # Application constants
│   │   └── constant.py       # UI themes and database paths
│   │
│   ├── data/                 # Data layer
│   │   ├── database.db       # SQLite database
│   │   ├── dataloader.py     # Data loading utilities
│   │   ├── metadata.pkl      # Processed metadata
│   │   ├── sql_vectors.faiss # Vector embeddings for SQL
│   │   ├── datasets/         # CSV data files
│   │   ├── documents/        # PDF documents (pizza menu items)
│   │   └── retriever/        # FAISS vector store
│   │
│   ├── tools/                # AI agent tools
│   │   ├── tools_agents.py   # Main agent implementation
│   │   └── fallback_agents.py # Fallback agent system
│   │
│   ├── utils/                # Utility functions
│   │   ├── llm_setup.py      # Language model configuration
│   │   ├── tools.py          # Custom tools for agents
│   │   ├── utils.py          # General utilities
│   │   └── fallback_tools.py # Fallback tool implementations
│   │
│   └── logs/                 # Application logs
│       └── app.log
```

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- Azure OpenAI API access
- Required Python packages (see requirements.txt)

### Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd SalesPulse
   ```

2. **Set up virtual environment**:

   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   # or
   source venv/bin/activate  # Linux/Mac
   ```

3. **Install dependencies**:

   ```bash
   pip install -r SalesPulse3.0.0/v3.0.0/requirements.txt
   ```

4. **Configure environment variables**:

   Create a `.env` file in the `v3.0.0` directory with:

   ```env
   AZURE_OPENAI_API_KEY=your_api_key
   AZURE_OPENAI_ENDPOINT=your_endpoint
   AZURE_OPENAI_API_VERSION=your_api_version
   ```

5. **Run the application**:

   ```bash
   cd SalesPulse3.0.0/v3.0.0
   streamlit run assistant.py
   ```

## 📊 Data Sources

### Database Tables

- **Pizza Data**: Sales transactions and product information
- **Event Data**: Special events and promotions
- **Forecast Data**: Sales predictions and trends
- **Store Opening Data**: Store locations and opening details
- **Weather Data**: Weather impact on sales
- **Weekly Anomalies**: Unusual sales patterns

### Document Collection

- PDF documents for each pizza variety with detailed descriptions
- Menu items, ingredients, and nutritional information
- Marketing materials and product specifications

## 🔧 Core Components

### 1. **Assistant (`assistant.py`)**

- Streamlit-based web interface
- User authentication system
- Session management
- Chat history and conversation flow
- Error handling and user feedback

### 2. **Main Processing (`main.py`)**

- Query processing orchestration
- Fallback detection and handling
- Agent coordination
- Response validation and formatting

### 3. **Data Layer (`data/`)**

- **`dataloader.py`**: Manages data loading and caching
- **`database.db`**: SQLite database with business data
- **Vector stores**: FAISS indices for semantic search
- **Metadata**: Processed data descriptions and schemas

### 4. **AI Agents (`tools/`)**

- **Primary Agent**: Main query processing with SQL generation
- **Fallback Agent**: Handles complex queries and edge cases
- **Tool Integration**: Custom tools for data analysis and visualization

### 5. **Configuration (`configuration/`)**

- **`prompts.yaml`**: Structured prompts for different scenarios
- **`config.py`**: Session and application configuration
- System instructions and behavior definitions

## 🛠️ Key Technologies

- **Frontend**: Streamlit for interactive web interface
- **Backend**: Python with LangChain for AI orchestration
- **Database**: SQLite for structured data storage
- **Vector Database**: FAISS for semantic search
- **AI Models**: Azure OpenAI (GPT-4, embeddings)
- **Visualization**: Matplotlib and Streamlit charts
- **Authentication**: Custom session-based authentication

## 📈 Features in Detail

### Natural Language Querying

- Convert business questions to SQL automatically
- Support for complex analytical queries
- Intelligent query optimization and validation

### Intelligent Fallback System

- Detects when primary agent reaches limitations
- Seamlessly transitions to fallback processing
- Maintains conversation context and user experience

### Multi-Modal Responses

- Text-based insights and explanations
- Dynamic charts and visualizations
- Document retrieval and citation
- Statistical analysis and trends

### Business Intelligence Capabilities

- Sales performance analysis
- Trend identification and forecasting
- Anomaly detection and alerting
- Competitive analysis and benchmarking

## 🔒 Security & Authentication

- Session-based authentication system
- User access control and management
- Secure API key handling
- Request rate limiting and validation

## 📝 Logging & Monitoring

- Comprehensive logging system (`logger.py`)
- Request/response tracking
- Performance monitoring
- Error tracking and debugging

## 🚧 Development

### Running in Development Mode

```bash
# Navigate to the project directory
cd SalesPulse3.0.0/v3.0.0

# Run with development settings
streamlit run assistant.py --server.runOnSave true
```

### Jupyter Notebook

The `workbook.ipynb` file contains development experiments and testing code for various components.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary software developed for pizza chain business intelligence.

## 🆘 Support

For technical support or questions about the SalesPulse system, please contact the development team or refer to the internal documentation.

---

**Version**: 3.0.0  
**Last Updated**: July 2025  
**Developed by**: Nihilent Technologies
