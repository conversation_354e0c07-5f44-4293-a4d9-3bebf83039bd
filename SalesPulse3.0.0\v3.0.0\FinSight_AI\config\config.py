"""
FinSight AI Configuration Module
Configuration settings for the PDF-based financial document analysis system
"""

import os
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class CompanyConfig:
    """Configuration for each company"""
    name: str
    display_name: str
    color: str
    description: str

@dataclass
class ModelConfig:
    """LLM and embedding model configuration"""
    embedding_model: str = "BAAI/bge-base-en-v1.5"
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_tokens: int = 2000
    temperature: float = 0.1
    top_k_retrieval: int = 5

@dataclass
class SystemConfig:
    """System-wide configuration"""
    base_dir: Path
    documents_dir: Path
    vectors_dir: Path
    processed_dir: Path
    logs_dir: Path
    
    # Company configurations
    companies: Dict[str, CompanyConfig]
    
    # Model configurations
    model_config: ModelConfig
    
    # UI Configuration
    page_title: str = "FinSight AI - Annual Report Analysis"
    page_icon: str = "📊"
    
    def __post_init__(self):
        """Ensure all directories exist"""
        for directory in [self.documents_dir, self.vectors_dir, self.processed_dir, self.logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Create company-specific document directories
        for company_key in self.companies.keys():
            company_doc_dir = self.documents_dir / company_key
            company_doc_dir.mkdir(parents=True, exist_ok=True)

def get_system_config() -> SystemConfig:
    """Get the system configuration"""
    
    # Get base directory (FinSight_AI folder)
    base_dir = Path(__file__).parent.parent
    
    # Define company configurations
    companies = {
        "castrol": CompanyConfig(
            name="castrol",
            display_name="Castrol",
            color="#00A651",  # Castrol green
            description="Leading automotive and industrial lubricants company"
        ),
        "veedol": CompanyConfig(
            name="veedol",
            display_name="Veedol",
            color="#FF6B35",  # Veedol orange
            description="Premium motor oil and lubricants brand"
        ),
        "valvoline": CompanyConfig(
            name="valvoline",
            display_name="Valvoline",
            color="#E31837",  # Valvoline red
            description="Global leader in automotive lubricants and services"
        )
    }
    
    return SystemConfig(
        base_dir=base_dir,
        documents_dir=base_dir / "documents",
        vectors_dir=base_dir / "data" / "vectors",
        processed_dir=base_dir / "data" / "processed",
        logs_dir=base_dir / "logs",
        companies=companies,
        model_config=ModelConfig()
    )

# Environment variables for Azure OpenAI (if using)
AZURE_CONFIG = {
    "api_key": os.getenv("AZURE_OPENAI_API_KEY"),
    "endpoint": os.getenv("AZURE_OPENAI_ENDPOINT"),
    "api_version": os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview"),
    "deployment_name": os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
}

# Supported file types
SUPPORTED_FILE_TYPES = [".pdf"]

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler",
        },
        "file": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": "logs/finsight_ai.log",
            "mode": "a",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": False
        }
    }
}

# Streamlit page configuration
STREAMLIT_CONFIG = {
    "page_title": "FinSight AI",
    "page_icon": "📊",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Analysis prompts for different types of queries
ANALYSIS_PROMPTS = {
    "financial_performance": """
    Analyze the financial performance based on the provided context from annual reports.
    Focus on:
    - Revenue trends and growth
    - Profitability metrics
    - Key financial ratios
    - Year-over-year comparisons
    - Notable financial highlights or concerns
    
    Provide specific numbers and percentages where available.
    """,
    
    "business_strategy": """
    Analyze the business strategy and strategic initiatives based on the provided context.
    Focus on:
    - Strategic priorities and objectives
    - Market expansion plans
    - Investment areas
    - Competitive positioning
    - Future outlook and guidance
    
    Highlight key strategic decisions and their expected impact.
    """,
    
    "risk_analysis": """
    Analyze the risk factors and risk management approach based on the provided context.
    Focus on:
    - Key risk factors identified
    - Risk mitigation strategies
    - Market and operational risks
    - Regulatory and compliance risks
    - Financial and credit risks
    
    Summarize the most significant risks and management's approach to addressing them.
    """,
    
    "sustainability": """
    Analyze the sustainability and ESG (Environmental, Social, Governance) initiatives.
    Focus on:
    - Environmental initiatives and targets
    - Social responsibility programs
    - Governance practices
    - Sustainability metrics and progress
    - ESG commitments and achievements
    
    Highlight key sustainability goals and progress made.
    """,
    
    "comparative": """
    Provide a comparative analysis across the companies based on the provided context.
    Focus on:
    - Financial performance comparison
    - Strategic positioning differences
    - Market share and competitive advantages
    - Operational efficiency metrics
    - Growth trajectories
    
    Identify key differentiators and relative strengths/weaknesses.
    """
}
